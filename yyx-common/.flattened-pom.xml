<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ydwl</groupId>
    <artifactId>ydwl-live</artifactId>
    <version>5.4.0</version>
  </parent>
  <groupId>com.ydwl</groupId>
  <artifactId>yyx-common</artifactId>
  <version>5.4.0</version>
  <packaging>pom</packaging>
  <description>common 通用模块</description>
  <modules>
    <module>yyx-common-bom</module>
    <module>yyx-common-social</module>
    <module>yyx-common-core</module>
    <module>yyx-common-doc</module>
    <module>yyx-common-excel</module>
    <module>yyx-common-idempotent</module>
    <module>yyx-common-job</module>
    <module>yyx-common-log</module>
    <module>yyx-common-mail</module>
    <module>yyx-common-mybatis</module>
    <module>yyx-common-oss</module>
    <module>yyx-common-ratelimiter</module>
    <module>yyx-common-redis</module>
    <module>yyx-common-satoken</module>
    <module>yyx-common-security</module>
    <module>yyx-common-sms</module>
    <module>yyx-common-web</module>
    <module>yyx-common-translation</module>
    <module>yyx-common-sensitive</module>
    <module>yyx-common-json</module>
    <module>yyx-common-encrypt</module>
    <module>yyx-common-tenant</module>
    <module>yyx-common-websocket</module>
    <module>yyx-common-sse</module>
  </modules>
</project>
