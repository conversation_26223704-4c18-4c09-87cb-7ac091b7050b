<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ydwl</groupId>
    <artifactId>yyx-common</artifactId>
    <version>5.4.0</version>
  </parent>
  <groupId>com.ydwl</groupId>
  <artifactId>yyx-common-websocket</artifactId>
  <version>5.4.0</version>
  <description>yyx-common-websocket 模块</description>
  <dependencies>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-satoken</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-json</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-tomcat</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
</project>
