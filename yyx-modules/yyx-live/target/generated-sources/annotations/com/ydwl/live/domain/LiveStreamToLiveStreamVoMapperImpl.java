package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveStreamVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveStreamToLiveStreamVoMapperImpl implements LiveStreamToLiveStreamVoMapper {

    @Override
    public LiveStreamVo convert(LiveStream arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveStreamVo liveStreamVo = new LiveStreamVo();

        liveStreamVo.setId( arg0.getId() );
        liveStreamVo.setLiveId( arg0.getLiveId() );
        liveStreamVo.setPushUrl( arg0.getPushUrl() );
        liveStreamVo.setPushKey( arg0.getPushKey() );
        liveStreamVo.setStreamStatus( arg0.getStreamStatus() );

        return liveStreamVo;
    }

    @Override
    public LiveStreamVo convert(LiveStream arg0, LiveStreamVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setPushUrl( arg0.getPushUrl() );
        arg1.setPushKey( arg0.getPushKey() );
        arg1.setStreamStatus( arg0.getStreamStatus() );

        return arg1;
    }
}
