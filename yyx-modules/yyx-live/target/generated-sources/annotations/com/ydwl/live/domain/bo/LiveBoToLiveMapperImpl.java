package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.Live;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveBoToLiveMapperImpl implements LiveBoToLiveMapper {

    @Override
    public Live convert(LiveBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Live live = new Live();

        live.setSearchValue( arg0.getSearchValue() );
        live.setCreateDept( arg0.getCreateDept() );
        live.setCreateBy( arg0.getCreateBy() );
        live.setCreateTime( arg0.getCreateTime() );
        live.setUpdateBy( arg0.getUpdateBy() );
        live.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            live.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        live.setId( arg0.getId() );
        live.setCategoryId( arg0.getCategoryId() );
        live.setTitle( arg0.getTitle() );
        live.setCoverImgUrl( arg0.getCoverImgUrl() );
        live.setPlanStartTime( arg0.getPlanStartTime() );
        live.setActualStartTime( arg0.getActualStartTime() );
        live.setActualEndTime( arg0.getActualEndTime() );
        live.setDurationMinutes( arg0.getDurationMinutes() );
        live.setStatus( arg0.getStatus() );
        live.setStreamId( arg0.getStreamId() );
        live.setSettingSignupRequired( arg0.getSettingSignupRequired() );
        live.setSettingReplayEnabled( arg0.getSettingReplayEnabled() );
        live.setSettingAutoRecord( arg0.getSettingAutoRecord() );
        live.setSettingChatEnabled( arg0.getSettingChatEnabled() );
        live.setSettingChatDelay( arg0.getSettingChatDelay() );
        live.setSettingGiftEnabled( arg0.getSettingGiftEnabled() );
        live.setSettingDefaultQuality( arg0.getSettingDefaultQuality() );
        live.setSettingAccessLevel( arg0.getSettingAccessLevel() );
        live.setDescription( arg0.getDescription() );
        live.setTagList( arg0.getTagList() );
        live.setRecordVideoUrl( arg0.getRecordVideoUrl() );
        live.setViewCount( arg0.getViewCount() );
        live.setLikeCount( arg0.getLikeCount() );
        live.setShareCount( arg0.getShareCount() );
        live.setMaxOnlineCount( arg0.getMaxOnlineCount() );
        live.setIsFeatured( arg0.getIsFeatured() );
        live.setHasReplay( arg0.getHasReplay() );

        return live;
    }

    @Override
    public Live convert(LiveBo arg0, Live arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setCategoryId( arg0.getCategoryId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setPlanStartTime( arg0.getPlanStartTime() );
        arg1.setActualStartTime( arg0.getActualStartTime() );
        arg1.setActualEndTime( arg0.getActualEndTime() );
        arg1.setDurationMinutes( arg0.getDurationMinutes() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setStreamId( arg0.getStreamId() );
        arg1.setSettingSignupRequired( arg0.getSettingSignupRequired() );
        arg1.setSettingReplayEnabled( arg0.getSettingReplayEnabled() );
        arg1.setSettingAutoRecord( arg0.getSettingAutoRecord() );
        arg1.setSettingChatEnabled( arg0.getSettingChatEnabled() );
        arg1.setSettingChatDelay( arg0.getSettingChatDelay() );
        arg1.setSettingGiftEnabled( arg0.getSettingGiftEnabled() );
        arg1.setSettingDefaultQuality( arg0.getSettingDefaultQuality() );
        arg1.setSettingAccessLevel( arg0.getSettingAccessLevel() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setTagList( arg0.getTagList() );
        arg1.setRecordVideoUrl( arg0.getRecordVideoUrl() );
        arg1.setViewCount( arg0.getViewCount() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setShareCount( arg0.getShareCount() );
        arg1.setMaxOnlineCount( arg0.getMaxOnlineCount() );
        arg1.setIsFeatured( arg0.getIsFeatured() );
        arg1.setHasReplay( arg0.getHasReplay() );

        return arg1;
    }
}
