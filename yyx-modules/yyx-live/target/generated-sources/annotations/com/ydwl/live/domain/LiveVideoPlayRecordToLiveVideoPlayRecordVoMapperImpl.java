package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveVideoPlayRecordVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveVideoPlayRecordToLiveVideoPlayRecordVoMapperImpl implements LiveVideoPlayRecordToLiveVideoPlayRecordVoMapper {

    @Override
    public LiveVideoPlayRecordVo convert(LiveVideoPlayRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveVideoPlayRecordVo liveVideoPlayRecordVo = new LiveVideoPlayRecordVo();

        liveVideoPlayRecordVo.setId( arg0.getId() );
        liveVideoPlayRecordVo.setLiveId( arg0.getLiveId() );
        liveVideoPlayRecordVo.setUserId( arg0.getUserId() );
        liveVideoPlayRecordVo.setPlayTime( arg0.getPlayTime() );
        liveVideoPlayRecordVo.setPlayDurationSeconds( arg0.getPlayDurationSeconds() );
        liveVideoPlayRecordVo.setLastPositionSeconds( arg0.getLastPositionSeconds() );
        liveVideoPlayRecordVo.setIsFinished( arg0.getIsFinished() );
        liveVideoPlayRecordVo.setDeviceType( arg0.getDeviceType() );
        liveVideoPlayRecordVo.setIpAddress( arg0.getIpAddress() );
        liveVideoPlayRecordVo.setVideoQuality( arg0.getVideoQuality() );

        return liveVideoPlayRecordVo;
    }

    @Override
    public LiveVideoPlayRecordVo convert(LiveVideoPlayRecord arg0, LiveVideoPlayRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setPlayTime( arg0.getPlayTime() );
        arg1.setPlayDurationSeconds( arg0.getPlayDurationSeconds() );
        arg1.setLastPositionSeconds( arg0.getLastPositionSeconds() );
        arg1.setIsFinished( arg0.getIsFinished() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setIpAddress( arg0.getIpAddress() );
        arg1.setVideoQuality( arg0.getVideoQuality() );

        return arg1;
    }
}
