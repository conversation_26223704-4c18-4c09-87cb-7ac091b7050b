package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeOutput;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeOutputVoToLiveTranscodeOutputMapperImpl implements LiveTranscodeOutputVoToLiveTranscodeOutputMapper {

    @Override
    public LiveTranscodeOutput convert(LiveTranscodeOutputVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeOutput liveTranscodeOutput = new LiveTranscodeOutput();

        liveTranscodeOutput.setId( arg0.getId() );
        liveTranscodeOutput.setTaskId( arg0.getTaskId() );
        liveTranscodeOutput.setDefinition( arg0.getDefinition() );
        liveTranscodeOutput.setResolution( arg0.getResolution() );
        liveTranscodeOutput.setFormat( arg0.getFormat() );
        liveTranscodeOutput.setUrl( arg0.getUrl() );
        liveTranscodeOutput.setSegmentTime( arg0.getSegmentTime() );
        liveTranscodeOutput.setFileSize( arg0.getFileSize() );
        liveTranscodeOutput.setBitRate( arg0.getBitRate() );
        liveTranscodeOutput.setDuration( arg0.getDuration() );

        return liveTranscodeOutput;
    }

    @Override
    public LiveTranscodeOutput convert(LiveTranscodeOutputVo arg0, LiveTranscodeOutput arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setDefinition( arg0.getDefinition() );
        arg1.setResolution( arg0.getResolution() );
        arg1.setFormat( arg0.getFormat() );
        arg1.setUrl( arg0.getUrl() );
        arg1.setSegmentTime( arg0.getSegmentTime() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setBitRate( arg0.getBitRate() );
        arg1.setDuration( arg0.getDuration() );

        return arg1;
    }
}
