package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveRoomMember;
import com.ydwl.live.domain.LiveRoomMemberToLiveRoomMemberVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveRoomMemberToLiveRoomMemberVoMapper.class},
    imports = {}
)
public interface LiveRoomMemberVoToLiveRoomMemberMapper extends BaseMapper<LiveRoomMemberVo, LiveRoomMember> {
}
