package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveUserWallet;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveUserWalletBoToLiveUserWalletMapperImpl implements LiveUserWalletBoToLiveUserWalletMapper {

    @Override
    public LiveUserWallet convert(LiveUserWalletBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveUserWallet liveUserWallet = new LiveUserWallet();

        liveUserWallet.setSearchValue( arg0.getSearchValue() );
        liveUserWallet.setCreateDept( arg0.getCreateDept() );
        liveUserWallet.setCreateBy( arg0.getCreateBy() );
        liveUserWallet.setCreateTime( arg0.getCreateTime() );
        liveUserWallet.setUpdateBy( arg0.getUpdateBy() );
        liveUserWallet.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveUserWallet.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveUserWallet.setId( arg0.getId() );
        liveUserWallet.setUserId( arg0.getUserId() );
        liveUserWallet.setBalance( arg0.getBalance() );
        liveUserWallet.setTotalRecharge( arg0.getTotalRecharge() );
        liveUserWallet.setTotalConsume( arg0.getTotalConsume() );

        return liveUserWallet;
    }

    @Override
    public LiveUserWallet convert(LiveUserWalletBo arg0, LiveUserWallet arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBalance( arg0.getBalance() );
        arg1.setTotalRecharge( arg0.getTotalRecharge() );
        arg1.setTotalConsume( arg0.getTotalConsume() );

        return arg1;
    }
}
