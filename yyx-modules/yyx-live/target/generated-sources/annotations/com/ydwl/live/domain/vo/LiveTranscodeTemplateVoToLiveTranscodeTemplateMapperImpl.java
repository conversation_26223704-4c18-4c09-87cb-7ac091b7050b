package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeTemplate;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeTemplateVoToLiveTranscodeTemplateMapperImpl implements LiveTranscodeTemplateVoToLiveTranscodeTemplateMapper {

    @Override
    public LiveTranscodeTemplate convert(LiveTranscodeTemplateVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeTemplate liveTranscodeTemplate = new LiveTranscodeTemplate();

        liveTranscodeTemplate.setId( arg0.getId() );
        liveTranscodeTemplate.setTemplateName( arg0.getTemplateName() );
        liveTranscodeTemplate.setTemplateType( arg0.getTemplateType() );
        liveTranscodeTemplate.setDefinition( arg0.getDefinition() );
        liveTranscodeTemplate.setResolution( arg0.getResolution() );
        liveTranscodeTemplate.setVideoBitrate( arg0.getVideoBitrate() );
        liveTranscodeTemplate.setAudioBitrate( arg0.getAudioBitrate() );
        liveTranscodeTemplate.setFps( arg0.getFps() );
        liveTranscodeTemplate.setFormat( arg0.getFormat() );
        liveTranscodeTemplate.setCodec( arg0.getCodec() );
        liveTranscodeTemplate.setExternalId( arg0.getExternalId() );
        liveTranscodeTemplate.setStatus( arg0.getStatus() );
        liveTranscodeTemplate.setIsDefault( arg0.getIsDefault() );

        return liveTranscodeTemplate;
    }

    @Override
    public LiveTranscodeTemplate convert(LiveTranscodeTemplateVo arg0, LiveTranscodeTemplate arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTemplateName( arg0.getTemplateName() );
        arg1.setTemplateType( arg0.getTemplateType() );
        arg1.setDefinition( arg0.getDefinition() );
        arg1.setResolution( arg0.getResolution() );
        arg1.setVideoBitrate( arg0.getVideoBitrate() );
        arg1.setAudioBitrate( arg0.getAudioBitrate() );
        arg1.setFps( arg0.getFps() );
        arg1.setFormat( arg0.getFormat() );
        arg1.setCodec( arg0.getCodec() );
        arg1.setExternalId( arg0.getExternalId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setIsDefault( arg0.getIsDefault() );

        return arg1;
    }
}
