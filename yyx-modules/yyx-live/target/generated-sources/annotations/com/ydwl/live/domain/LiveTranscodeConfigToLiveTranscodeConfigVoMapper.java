package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveTranscodeConfigBoToLiveTranscodeConfigMapper;
import com.ydwl.live.domain.vo.LiveTranscodeConfigVo;
import com.ydwl.live.domain.vo.LiveTranscodeConfigVoToLiveTranscodeConfigMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTranscodeConfigBoToLiveTranscodeConfigMapper.class,LiveTranscodeConfigVoToLiveTranscodeConfigMapper.class},
    imports = {}
)
public interface LiveTranscodeConfigToLiveTranscodeConfigVoMapper extends BaseMapper<LiveTranscodeConfig, LiveTranscodeConfigVo> {
}
