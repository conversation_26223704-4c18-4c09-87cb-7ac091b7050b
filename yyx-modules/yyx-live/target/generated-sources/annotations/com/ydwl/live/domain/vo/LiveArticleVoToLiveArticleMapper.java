package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveArticle;
import com.ydwl.live.domain.LiveArticleToLiveArticleVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveArticleToLiveArticleVoMapper.class},
    imports = {}
)
public interface LiveArticleVoToLiveArticleMapper extends BaseMapper<LiveArticleVo, LiveArticle> {
}
