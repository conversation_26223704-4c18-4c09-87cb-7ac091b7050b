package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveTransaction;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTransactionBoToLiveTransactionMapperImpl implements LiveTransactionBoToLiveTransactionMapper {

    @Override
    public LiveTransaction convert(LiveTransactionBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTransaction liveTransaction = new LiveTransaction();

        liveTransaction.setSearchValue( arg0.getSearchValue() );
        liveTransaction.setCreateDept( arg0.getCreateDept() );
        liveTransaction.setCreateBy( arg0.getCreateBy() );
        liveTransaction.setCreateTime( arg0.getCreateTime() );
        liveTransaction.setUpdateBy( arg0.getUpdateBy() );
        liveTransaction.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveTransaction.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveTransaction.setId( arg0.getId() );
        liveTransaction.setUserId( arg0.getUserId() );
        liveTransaction.setRelatedId( arg0.getRelatedId() );
        liveTransaction.setType( arg0.getType() );
        liveTransaction.setAmount( arg0.getAmount() );
        liveTransaction.setBalance( arg0.getBalance() );
        liveTransaction.setDetail( arg0.getDetail() );
        liveTransaction.setTargetType( arg0.getTargetType() );
        liveTransaction.setTargetId( arg0.getTargetId() );

        return liveTransaction;
    }

    @Override
    public LiveTransaction convert(LiveTransactionBo arg0, LiveTransaction arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setRelatedId( arg0.getRelatedId() );
        arg1.setType( arg0.getType() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setBalance( arg0.getBalance() );
        arg1.setDetail( arg0.getDetail() );
        arg1.setTargetType( arg0.getTargetType() );
        arg1.setTargetId( arg0.getTargetId() );

        return arg1;
    }
}
