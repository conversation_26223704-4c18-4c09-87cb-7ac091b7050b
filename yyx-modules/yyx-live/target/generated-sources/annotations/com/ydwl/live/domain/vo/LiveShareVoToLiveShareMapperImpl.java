package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveShare;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveShareVoToLiveShareMapperImpl implements LiveShareVoToLiveShareMapper {

    @Override
    public LiveShare convert(LiveShareVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveShare liveShare = new LiveShare();

        liveShare.setId( arg0.getId() );
        liveShare.setLiveId( arg0.getLiveId() );
        liveShare.setUserId( arg0.getUserId() );
        liveShare.setSharePlatform( arg0.getSharePlatform() );

        return liveShare;
    }

    @Override
    public LiveShare convert(LiveShareVo arg0, LiveShare arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setSharePlatform( arg0.getSharePlatform() );

        return arg1;
    }
}
