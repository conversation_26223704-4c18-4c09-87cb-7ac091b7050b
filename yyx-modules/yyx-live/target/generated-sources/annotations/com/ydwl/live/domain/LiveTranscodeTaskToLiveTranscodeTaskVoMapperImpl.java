package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveTranscodeTaskVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeTaskToLiveTranscodeTaskVoMapperImpl implements LiveTranscodeTaskToLiveTranscodeTaskVoMapper {

    @Override
    public LiveTranscodeTaskVo convert(LiveTranscodeTask arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeTaskVo liveTranscodeTaskVo = new LiveTranscodeTaskVo();

        liveTranscodeTaskVo.setId( arg0.getId() );
        liveTranscodeTaskVo.setTaskNo( arg0.getTaskNo() );
        liveTranscodeTaskVo.setTaskType( arg0.getTaskType() );
        liveTranscodeTaskVo.setLiveId( arg0.getLiveId() );
        liveTranscodeTaskVo.setSourceUrl( arg0.getSourceUrl() );
        liveTranscodeTaskVo.setStatus( arg0.getStatus() );
        liveTranscodeTaskVo.setTemplateId( arg0.getTemplateId() );
        liveTranscodeTaskVo.setBucket( arg0.getBucket() );
        liveTranscodeTaskVo.setObjectKey( arg0.getObjectKey() );
        liveTranscodeTaskVo.setOutputUrl( arg0.getOutputUrl() );
        liveTranscodeTaskVo.setDuration( arg0.getDuration() );
        liveTranscodeTaskVo.setFileSize( arg0.getFileSize() );
        liveTranscodeTaskVo.setProgress( arg0.getProgress() );
        liveTranscodeTaskVo.setBizId( arg0.getBizId() );
        liveTranscodeTaskVo.setCallbackUrl( arg0.getCallbackUrl() );
        liveTranscodeTaskVo.setCallbackStatus( arg0.getCallbackStatus() );
        liveTranscodeTaskVo.setStartTime( arg0.getStartTime() );
        liveTranscodeTaskVo.setEndTime( arg0.getEndTime() );
        liveTranscodeTaskVo.setErrorMsg( arg0.getErrorMsg() );
        liveTranscodeTaskVo.setRetryCount( arg0.getRetryCount() );
        liveTranscodeTaskVo.setPriority( arg0.getPriority() );

        return liveTranscodeTaskVo;
    }

    @Override
    public LiveTranscodeTaskVo convert(LiveTranscodeTask arg0, LiveTranscodeTaskVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTaskNo( arg0.getTaskNo() );
        arg1.setTaskType( arg0.getTaskType() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setSourceUrl( arg0.getSourceUrl() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTemplateId( arg0.getTemplateId() );
        arg1.setBucket( arg0.getBucket() );
        arg1.setObjectKey( arg0.getObjectKey() );
        arg1.setOutputUrl( arg0.getOutputUrl() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setBizId( arg0.getBizId() );
        arg1.setCallbackUrl( arg0.getCallbackUrl() );
        arg1.setCallbackStatus( arg0.getCallbackStatus() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setErrorMsg( arg0.getErrorMsg() );
        arg1.setRetryCount( arg0.getRetryCount() );
        arg1.setPriority( arg0.getPriority() );

        return arg1;
    }
}
