package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveReplayBoToLiveReplayMapper;
import com.ydwl.live.domain.vo.LiveReplayVo;
import com.ydwl.live.domain.vo.LiveReplayVoToLiveReplayMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveReplayVoToLiveReplayMapper.class,LiveReplayBoToLiveReplayMapper.class},
    imports = {}
)
public interface LiveReplayToLiveReplayVoMapper extends BaseMapper<LiveReplay, LiveReplayVo> {
}
