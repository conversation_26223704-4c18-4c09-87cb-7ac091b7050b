package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveUserSubscribe;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveUserSubscribeBoToLiveUserSubscribeMapperImpl implements LiveUserSubscribeBoToLiveUserSubscribeMapper {

    @Override
    public LiveUserSubscribe convert(LiveUserSubscribeBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveUserSubscribe liveUserSubscribe = new LiveUserSubscribe();

        liveUserSubscribe.setSearchValue( arg0.getSearchValue() );
        liveUserSubscribe.setCreateDept( arg0.getCreateDept() );
        liveUserSubscribe.setCreateBy( arg0.getCreateBy() );
        liveUserSubscribe.setCreateTime( arg0.getCreateTime() );
        liveUserSubscribe.setUpdateBy( arg0.getUpdateBy() );
        liveUserSubscribe.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveUserSubscribe.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveUserSubscribe.setId( arg0.getId() );
        liveUserSubscribe.setUserId( arg0.getUserId() );
        liveUserSubscribe.setLiveId( arg0.getLiveId() );
        liveUserSubscribe.setSubscribeTime( arg0.getSubscribeTime() );
        liveUserSubscribe.setIsNotified( arg0.getIsNotified() );

        return liveUserSubscribe;
    }

    @Override
    public LiveUserSubscribe convert(LiveUserSubscribeBo arg0, LiveUserSubscribe arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setSubscribeTime( arg0.getSubscribeTime() );
        arg1.setIsNotified( arg0.getIsNotified() );

        return arg1;
    }
}
