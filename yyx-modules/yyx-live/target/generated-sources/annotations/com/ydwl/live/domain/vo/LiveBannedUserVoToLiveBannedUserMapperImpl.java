package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveBannedUser;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveBannedUserVoToLiveBannedUserMapperImpl implements LiveBannedUserVoToLiveBannedUserMapper {

    @Override
    public LiveBannedUser convert(LiveBannedUserVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveBannedUser liveBannedUser = new LiveBannedUser();

        liveBannedUser.setId( arg0.getId() );
        liveBannedUser.setLiveId( arg0.getLiveId() );
        liveBannedUser.setUserId( arg0.getUserId() );
        liveBannedUser.setBanReason( arg0.getBanReason() );
        liveBannedUser.setOperatorId( arg0.getOperatorId() );
        liveBannedUser.setBanTime( arg0.getBanTime() );
        liveBannedUser.setUnbanTime( arg0.getUnbanTime() );
        liveBannedUser.setBanStatus( arg0.getBanStatus() );

        return liveBannedUser;
    }

    @Override
    public LiveBannedUser convert(LiveBannedUserVo arg0, LiveBannedUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBanReason( arg0.getBanReason() );
        arg1.setOperatorId( arg0.getOperatorId() );
        arg1.setBanTime( arg0.getBanTime() );
        arg1.setUnbanTime( arg0.getUnbanTime() );
        arg1.setBanStatus( arg0.getBanStatus() );

        return arg1;
    }
}
