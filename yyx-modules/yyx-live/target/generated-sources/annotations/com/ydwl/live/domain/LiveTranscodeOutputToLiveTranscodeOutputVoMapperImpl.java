package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveTranscodeOutputVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeOutputToLiveTranscodeOutputVoMapperImpl implements LiveTranscodeOutputToLiveTranscodeOutputVoMapper {

    @Override
    public LiveTranscodeOutputVo convert(LiveTranscodeOutput arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeOutputVo liveTranscodeOutputVo = new LiveTranscodeOutputVo();

        liveTranscodeOutputVo.setId( arg0.getId() );
        liveTranscodeOutputVo.setTaskId( arg0.getTaskId() );
        liveTranscodeOutputVo.setDefinition( arg0.getDefinition() );
        liveTranscodeOutputVo.setResolution( arg0.getResolution() );
        liveTranscodeOutputVo.setFormat( arg0.getFormat() );
        liveTranscodeOutputVo.setUrl( arg0.getUrl() );
        liveTranscodeOutputVo.setSegmentTime( arg0.getSegmentTime() );
        liveTranscodeOutputVo.setFileSize( arg0.getFileSize() );
        liveTranscodeOutputVo.setBitRate( arg0.getBitRate() );
        liveTranscodeOutputVo.setDuration( arg0.getDuration() );

        return liveTranscodeOutputVo;
    }

    @Override
    public LiveTranscodeOutputVo convert(LiveTranscodeOutput arg0, LiveTranscodeOutputVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setDefinition( arg0.getDefinition() );
        arg1.setResolution( arg0.getResolution() );
        arg1.setFormat( arg0.getFormat() );
        arg1.setUrl( arg0.getUrl() );
        arg1.setSegmentTime( arg0.getSegmentTime() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setBitRate( arg0.getBitRate() );
        arg1.setDuration( arg0.getDuration() );

        return arg1;
    }
}
