package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveArticle;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveArticleVoToLiveArticleMapperImpl implements LiveArticleVoToLiveArticleMapper {

    @Override
    public LiveArticle convert(LiveArticleVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveArticle liveArticle = new LiveArticle();

        liveArticle.setId( arg0.getId() );
        liveArticle.setRelatedId( arg0.getRelatedId() );
        liveArticle.setTitle( arg0.getTitle() );
        liveArticle.setContent( arg0.getContent() );
        liveArticle.setPublishStatus( arg0.getPublishStatus() );
        liveArticle.setRelatedType( arg0.getRelatedType() );
        liveArticle.setContentFormat( arg0.getContentFormat() );
        liveArticle.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveArticle.setSummary( arg0.getSummary() );
        liveArticle.setLastEditTime( arg0.getLastEditTime() );
        liveArticle.setIsFeatured( arg0.getIsFeatured() );
        liveArticle.setIsCommentAllowed( arg0.getIsCommentAllowed() );
        liveArticle.setViewCount( arg0.getViewCount() );
        liveArticle.setLikeCount( arg0.getLikeCount() );
        liveArticle.setShareCount( arg0.getShareCount() );

        return liveArticle;
    }

    @Override
    public LiveArticle convert(LiveArticleVo arg0, LiveArticle arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setRelatedId( arg0.getRelatedId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setContent( arg0.getContent() );
        arg1.setPublishStatus( arg0.getPublishStatus() );
        arg1.setRelatedType( arg0.getRelatedType() );
        arg1.setContentFormat( arg0.getContentFormat() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setLastEditTime( arg0.getLastEditTime() );
        arg1.setIsFeatured( arg0.getIsFeatured() );
        arg1.setIsCommentAllowed( arg0.getIsCommentAllowed() );
        arg1.setViewCount( arg0.getViewCount() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setShareCount( arg0.getShareCount() );

        return arg1;
    }
}
