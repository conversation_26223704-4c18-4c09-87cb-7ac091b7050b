package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveBannedUser;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveBannedUserBoToLiveBannedUserMapperImpl implements LiveBannedUserBoToLiveBannedUserMapper {

    @Override
    public LiveBannedUser convert(LiveBannedUserBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveBannedUser liveBannedUser = new LiveBannedUser();

        liveBannedUser.setSearchValue( arg0.getSearchValue() );
        liveBannedUser.setCreateDept( arg0.getCreateDept() );
        liveBannedUser.setCreateBy( arg0.getCreateBy() );
        liveBannedUser.setCreateTime( arg0.getCreateTime() );
        liveBannedUser.setUpdateBy( arg0.getUpdateBy() );
        liveBannedUser.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveBannedUser.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveBannedUser.setId( arg0.getId() );
        liveBannedUser.setLiveId( arg0.getLiveId() );
        liveBannedUser.setUserId( arg0.getUserId() );
        liveBannedUser.setBanReason( arg0.getBanReason() );
        liveBannedUser.setOperatorId( arg0.getOperatorId() );
        liveBannedUser.setBanTime( arg0.getBanTime() );
        liveBannedUser.setUnbanTime( arg0.getUnbanTime() );
        liveBannedUser.setBanStatus( arg0.getBanStatus() );

        return liveBannedUser;
    }

    @Override
    public LiveBannedUser convert(LiveBannedUserBo arg0, LiveBannedUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBanReason( arg0.getBanReason() );
        arg1.setOperatorId( arg0.getOperatorId() );
        arg1.setBanTime( arg0.getBanTime() );
        arg1.setUnbanTime( arg0.getUnbanTime() );
        arg1.setBanStatus( arg0.getBanStatus() );

        return arg1;
    }
}
