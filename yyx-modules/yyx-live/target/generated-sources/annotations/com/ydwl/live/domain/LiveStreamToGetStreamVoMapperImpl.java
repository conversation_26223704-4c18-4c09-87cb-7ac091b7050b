package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.GetStreamVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveStreamToGetStreamVoMapperImpl implements LiveStreamToGetStreamVoMapper {

    @Override
    public GetStreamVo convert(LiveStream arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GetStreamVo getStreamVo = new GetStreamVo();

        getStreamVo.setId( arg0.getId() );
        getStreamVo.setLiveId( arg0.getLiveId() );
        getStreamVo.setPushUrl( arg0.getPushUrl() );
        getStreamVo.setPushKey( arg0.getPushKey() );
        getStreamVo.setStreamStatus( arg0.getStreamStatus() );
        getStreamVo.setUpdateTime( arg0.getUpdateTime() );

        return getStreamVo;
    }

    @Override
    public GetStreamVo convert(LiveStream arg0, GetStreamVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setPushUrl( arg0.getPushUrl() );
        arg1.setPushKey( arg0.getPushKey() );
        arg1.setStreamStatus( arg0.getStreamStatus() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
