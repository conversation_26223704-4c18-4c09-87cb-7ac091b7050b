package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeOutput;
import com.ydwl.live.domain.LiveTranscodeOutputToLiveTranscodeOutputVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTranscodeOutputToLiveTranscodeOutputVoMapper.class},
    imports = {}
)
public interface LiveTranscodeOutputVoToLiveTranscodeOutputMapper extends BaseMapper<LiveTranscodeOutputVo, LiveTranscodeOutput> {
}
