package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveShareBoToLiveShareMapper;
import com.ydwl.live.domain.vo.LiveShareVo;
import com.ydwl.live.domain.vo.LiveShareVoToLiveShareMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveShareBoToLiveShareMapper.class,LiveShareVoToLiveShareMapper.class},
    imports = {}
)
public interface LiveShareToLiveShareVoMapper extends BaseMapper<LiveShare, LiveShareVo> {
}
