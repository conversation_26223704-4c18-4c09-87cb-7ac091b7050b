package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveReplay;
import com.ydwl.live.domain.LiveReplayToLiveReplayVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveReplayToLiveReplayVoMapper.class},
    imports = {}
)
public interface LiveReplayVoToLiveReplayMapper extends BaseMapper<LiveReplayVo, LiveReplay> {
}
