package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveScheduleVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveScheduleToLiveScheduleVoMapperImpl implements LiveScheduleToLiveScheduleVoMapper {

    @Override
    public LiveScheduleVo convert(LiveSchedule arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveScheduleVo liveScheduleVo = new LiveScheduleVo();

        liveScheduleVo.setId( arg0.getId() );
        liveScheduleVo.setTitle( arg0.getTitle() );
        liveScheduleVo.setHostUserId( arg0.getHostUserId() );
        liveScheduleVo.setCategoryId( arg0.getCategoryId() );
        liveScheduleVo.setDescription( arg0.getDescription() );
        liveScheduleVo.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveScheduleVo.setScheduledTime( arg0.getScheduledTime() );
        liveScheduleVo.setEstimatedDurationMinutes( arg0.getEstimatedDurationMinutes() );
        liveScheduleVo.setScheduleStatus( arg0.getScheduleStatus() );

        return liveScheduleVo;
    }

    @Override
    public LiveScheduleVo convert(LiveSchedule arg0, LiveScheduleVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setHostUserId( arg0.getHostUserId() );
        arg1.setCategoryId( arg0.getCategoryId() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setScheduledTime( arg0.getScheduledTime() );
        arg1.setEstimatedDurationMinutes( arg0.getEstimatedDurationMinutes() );
        arg1.setScheduleStatus( arg0.getScheduleStatus() );

        return arg1;
    }
}
