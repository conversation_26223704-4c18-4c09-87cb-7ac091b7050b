package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveReplayVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveReplayToLiveReplayVoMapperImpl implements LiveReplayToLiveReplayVoMapper {

    @Override
    public LiveReplayVo convert(LiveReplay arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveReplayVo liveReplayVo = new LiveReplayVo();

        liveReplayVo.setId( arg0.getId() );
        liveReplayVo.setLiveId( arg0.getLiveId() );
        liveReplayVo.setTranscodeTaskId( arg0.getTranscodeTaskId() );
        liveReplayVo.setReplayUrl( arg0.getReplayUrl() );
        liveReplayVo.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveReplayVo.setStatus( arg0.getStatus() );
        liveReplayVo.setDuration( arg0.getDuration() );
        liveReplayVo.setAvailableTime( arg0.getAvailableTime() );
        liveReplayVo.setExpiryTime( arg0.getExpiryTime() );
        liveReplayVo.setAccessType( arg0.getAccessType() );
        liveReplayVo.setViewCount( arg0.getViewCount() );

        return liveReplayVo;
    }

    @Override
    public LiveReplayVo convert(LiveReplay arg0, LiveReplayVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setTranscodeTaskId( arg0.getTranscodeTaskId() );
        arg1.setReplayUrl( arg0.getReplayUrl() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setAvailableTime( arg0.getAvailableTime() );
        arg1.setExpiryTime( arg0.getExpiryTime() );
        arg1.setAccessType( arg0.getAccessType() );
        arg1.setViewCount( arg0.getViewCount() );

        return arg1;
    }
}
