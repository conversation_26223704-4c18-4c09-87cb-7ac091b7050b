package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveCategory;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveCategoryVoToLiveCategoryMapperImpl implements LiveCategoryVoToLiveCategoryMapper {

    @Override
    public LiveCategory convert(LiveCategoryVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveCategory liveCategory = new LiveCategory();

        liveCategory.setId( arg0.getId() );
        liveCategory.setCategoryName( arg0.getCategoryName() );
        liveCategory.setCategoryCode( arg0.getCategoryCode() );
        liveCategory.setParentId( arg0.getParentId() );
        liveCategory.setPath( arg0.getPath() );
        liveCategory.setTreeSort( arg0.getTreeSort() );
        liveCategory.setTreeLevel( arg0.getTreeLevel() );
        liveCategory.setIconUrl( arg0.getIconUrl() );
        liveCategory.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveCategory.setDescription( arg0.getDescription() );
        liveCategory.setStatus( arg0.getStatus() );
        liveCategory.setLiveCount( arg0.getLiveCount() );
        liveCategory.setViewCount( arg0.getViewCount() );

        return liveCategory;
    }

    @Override
    public LiveCategory convert(LiveCategoryVo arg0, LiveCategory arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setCategoryName( arg0.getCategoryName() );
        arg1.setCategoryCode( arg0.getCategoryCode() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setPath( arg0.getPath() );
        arg1.setTreeSort( arg0.getTreeSort() );
        arg1.setTreeLevel( arg0.getTreeLevel() );
        arg1.setIconUrl( arg0.getIconUrl() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setLiveCount( arg0.getLiveCount() );
        arg1.setViewCount( arg0.getViewCount() );

        return arg1;
    }
}
