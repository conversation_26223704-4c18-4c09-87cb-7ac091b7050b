package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveCategoryVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveCategoryToLiveCategoryVoMapperImpl implements LiveCategoryToLiveCategoryVoMapper {

    @Override
    public LiveCategoryVo convert(LiveCategory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveCategoryVo liveCategoryVo = new LiveCategoryVo();

        liveCategoryVo.setId( arg0.getId() );
        liveCategoryVo.setCategoryName( arg0.getCategoryName() );
        liveCategoryVo.setCategoryCode( arg0.getCategoryCode() );
        liveCategoryVo.setParentId( arg0.getParentId() );
        liveCategoryVo.setPath( arg0.getPath() );
        liveCategoryVo.setTreeSort( arg0.getTreeSort() );
        liveCategoryVo.setTreeLevel( arg0.getTreeLevel() );
        liveCategoryVo.setIconUrl( arg0.getIconUrl() );
        liveCategoryVo.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveCategoryVo.setDescription( arg0.getDescription() );
        liveCategoryVo.setStatus( arg0.getStatus() );
        liveCategoryVo.setLiveCount( arg0.getLiveCount() );
        liveCategoryVo.setViewCount( arg0.getViewCount() );

        return liveCategoryVo;
    }

    @Override
    public LiveCategoryVo convert(LiveCategory arg0, LiveCategoryVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setCategoryName( arg0.getCategoryName() );
        arg1.setCategoryCode( arg0.getCategoryCode() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setPath( arg0.getPath() );
        arg1.setTreeSort( arg0.getTreeSort() );
        arg1.setTreeLevel( arg0.getTreeLevel() );
        arg1.setIconUrl( arg0.getIconUrl() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setLiveCount( arg0.getLiveCount() );
        arg1.setViewCount( arg0.getViewCount() );

        return arg1;
    }
}
