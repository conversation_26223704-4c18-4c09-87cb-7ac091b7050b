package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveVideoPlayRecord;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveVideoPlayRecordVoToLiveVideoPlayRecordMapperImpl implements LiveVideoPlayRecordVoToLiveVideoPlayRecordMapper {

    @Override
    public LiveVideoPlayRecord convert(LiveVideoPlayRecordVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveVideoPlayRecord liveVideoPlayRecord = new LiveVideoPlayRecord();

        liveVideoPlayRecord.setId( arg0.getId() );
        liveVideoPlayRecord.setLiveId( arg0.getLiveId() );
        liveVideoPlayRecord.setUserId( arg0.getUserId() );
        liveVideoPlayRecord.setPlayTime( arg0.getPlayTime() );
        liveVideoPlayRecord.setPlayDurationSeconds( arg0.getPlayDurationSeconds() );
        liveVideoPlayRecord.setLastPositionSeconds( arg0.getLastPositionSeconds() );
        liveVideoPlayRecord.setIsFinished( arg0.getIsFinished() );
        liveVideoPlayRecord.setDeviceType( arg0.getDeviceType() );
        liveVideoPlayRecord.setIpAddress( arg0.getIpAddress() );
        liveVideoPlayRecord.setVideoQuality( arg0.getVideoQuality() );

        return liveVideoPlayRecord;
    }

    @Override
    public LiveVideoPlayRecord convert(LiveVideoPlayRecordVo arg0, LiveVideoPlayRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setPlayTime( arg0.getPlayTime() );
        arg1.setPlayDurationSeconds( arg0.getPlayDurationSeconds() );
        arg1.setLastPositionSeconds( arg0.getLastPositionSeconds() );
        arg1.setIsFinished( arg0.getIsFinished() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setIpAddress( arg0.getIpAddress() );
        arg1.setVideoQuality( arg0.getVideoQuality() );

        return arg1;
    }
}
