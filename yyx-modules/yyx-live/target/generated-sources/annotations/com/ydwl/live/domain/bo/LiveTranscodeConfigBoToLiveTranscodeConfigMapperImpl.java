package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveTranscodeConfig;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeConfigBoToLiveTranscodeConfigMapperImpl implements LiveTranscodeConfigBoToLiveTranscodeConfigMapper {

    @Override
    public LiveTranscodeConfig convert(LiveTranscodeConfigBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeConfig liveTranscodeConfig = new LiveTranscodeConfig();

        liveTranscodeConfig.setSearchValue( arg0.getSearchValue() );
        liveTranscodeConfig.setCreateDept( arg0.getCreateDept() );
        liveTranscodeConfig.setCreateBy( arg0.getCreateBy() );
        liveTranscodeConfig.setCreateTime( arg0.getCreateTime() );
        liveTranscodeConfig.setUpdateBy( arg0.getUpdateBy() );
        liveTranscodeConfig.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveTranscodeConfig.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveTranscodeConfig.setId( arg0.getId() );
        liveTranscodeConfig.setKey( arg0.getKey() );
        liveTranscodeConfig.setValue( arg0.getValue() );
        liveTranscodeConfig.setType( arg0.getType() );
        liveTranscodeConfig.setDescription( arg0.getDescription() );

        return liveTranscodeConfig;
    }

    @Override
    public LiveTranscodeConfig convert(LiveTranscodeConfigBo arg0, LiveTranscodeConfig arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setKey( arg0.getKey() );
        arg1.setValue( arg0.getValue() );
        arg1.setType( arg0.getType() );
        arg1.setDescription( arg0.getDescription() );

        return arg1;
    }
}
