package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeOutputToLiveTranscodeOutputVoMapper;
import com.ydwl.live.domain.LiveTranscodeTask;
import com.ydwl.live.domain.LiveTranscodeTaskToLiveTranscodeTaskVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTranscodeOutputVoToLiveTranscodeOutputMapper.class,LiveTranscodeOutputToLiveTranscodeOutputVoMapper.class,LiveTranscodeTaskToLiveTranscodeTaskVoMapper.class},
    imports = {}
)
public interface LiveTranscodeTaskVoToLiveTranscodeTaskMapper extends BaseMapper<LiveTranscodeTaskVo, LiveTranscodeTask> {
}
