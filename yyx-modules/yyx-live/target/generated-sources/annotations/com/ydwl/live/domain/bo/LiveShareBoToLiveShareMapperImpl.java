package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveShare;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveShareBoToLiveShareMapperImpl implements LiveShareBoToLiveShareMapper {

    @Override
    public LiveShare convert(LiveShareBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveShare liveShare = new LiveShare();

        liveShare.setSearchValue( arg0.getSearchValue() );
        liveShare.setCreateDept( arg0.getCreateDept() );
        liveShare.setCreateBy( arg0.getCreateBy() );
        liveShare.setCreateTime( arg0.getCreateTime() );
        liveShare.setUpdateBy( arg0.getUpdateBy() );
        liveShare.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveShare.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveShare.setId( arg0.getId() );
        liveShare.setLiveId( arg0.getLiveId() );
        liveShare.setUserId( arg0.getUserId() );
        liveShare.setSharePlatform( arg0.getSharePlatform() );

        return liveShare;
    }

    @Override
    public LiveShare convert(LiveShareBo arg0, LiveShare arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setSharePlatform( arg0.getSharePlatform() );

        return arg1;
    }
}
