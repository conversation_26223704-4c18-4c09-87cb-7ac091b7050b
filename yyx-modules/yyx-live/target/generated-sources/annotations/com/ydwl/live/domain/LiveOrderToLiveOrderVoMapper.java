package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveOrderBoToLiveOrderMapper;
import com.ydwl.live.domain.vo.LiveOrderVo;
import com.ydwl.live.domain.vo.LiveOrderVoToLiveOrderMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveOrderBoToLiveOrderMapper.class,LiveOrderVoToLiveOrderMapper.class},
    imports = {}
)
public interface LiveOrderToLiveOrderVoMapper extends BaseMapper<LiveOrder, LiveOrderVo> {
}
