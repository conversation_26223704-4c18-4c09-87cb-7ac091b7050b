package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveSchedule;
import com.ydwl.live.domain.LiveScheduleToLiveScheduleVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveScheduleToLiveScheduleVoMapper.class},
    imports = {}
)
public interface LiveScheduleVoToLiveScheduleMapper extends BaseMapper<LiveScheduleVo, LiveSchedule> {
}
