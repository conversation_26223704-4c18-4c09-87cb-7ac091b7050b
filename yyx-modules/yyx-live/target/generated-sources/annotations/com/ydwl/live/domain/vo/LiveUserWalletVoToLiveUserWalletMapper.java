package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveUserWallet;
import com.ydwl.live.domain.LiveUserWalletToLiveUserWalletVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveUserWalletToLiveUserWalletVoMapper.class},
    imports = {}
)
public interface LiveUserWalletVoToLiveUserWalletMapper extends BaseMapper<LiveUserWalletVo, LiveUserWallet> {
}
