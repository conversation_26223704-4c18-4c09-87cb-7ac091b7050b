package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveStream;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveStreamBoToLiveStreamMapperImpl implements LiveStreamBoToLiveStreamMapper {

    @Override
    public LiveStream convert(LiveStreamBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveStream liveStream = new LiveStream();

        liveStream.setSearchValue( arg0.getSearchValue() );
        liveStream.setCreateDept( arg0.getCreateDept() );
        liveStream.setCreateBy( arg0.getCreateBy() );
        liveStream.setCreateTime( arg0.getCreateTime() );
        liveStream.setUpdateBy( arg0.getUpdateBy() );
        liveStream.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveStream.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveStream.setId( arg0.getId() );
        liveStream.setLiveId( arg0.getLiveId() );
        liveStream.setPushUrl( arg0.getPushUrl() );
        liveStream.setPushKey( arg0.getPushKey() );
        liveStream.setStreamStatus( arg0.getStreamStatus() );

        return liveStream;
    }

    @Override
    public LiveStream convert(LiveStreamBo arg0, LiveStream arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setPushUrl( arg0.getPushUrl() );
        arg1.setPushKey( arg0.getPushKey() );
        arg1.setStreamStatus( arg0.getStreamStatus() );

        return arg1;
    }
}
