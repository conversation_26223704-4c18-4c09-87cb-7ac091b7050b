package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveTransactionBoToLiveTransactionMapper;
import com.ydwl.live.domain.vo.LiveTransactionVo;
import com.ydwl.live.domain.vo.LiveTransactionVoToLiveTransactionMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTransactionBoToLiveTransactionMapper.class,LiveTransactionVoToLiveTransactionMapper.class},
    imports = {}
)
public interface LiveTransactionToLiveTransactionVoMapper extends BaseMapper<LiveTransaction, LiveTransactionVo> {
}
