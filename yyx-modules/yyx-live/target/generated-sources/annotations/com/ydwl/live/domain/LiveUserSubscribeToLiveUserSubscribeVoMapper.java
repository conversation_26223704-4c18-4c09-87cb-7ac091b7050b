package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveUserSubscribeBoToLiveUserSubscribeMapper;
import com.ydwl.live.domain.vo.LiveUserSubscribeVo;
import com.ydwl.live.domain.vo.LiveUserSubscribeVoToLiveUserSubscribeMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveUserSubscribeBoToLiveUserSubscribeMapper.class,LiveUserSubscribeVoToLiveUserSubscribeMapper.class},
    imports = {}
)
public interface LiveUserSubscribeToLiveUserSubscribeVoMapper extends BaseMapper<LiveUserSubscribe, LiveUserSubscribeVo> {
}
