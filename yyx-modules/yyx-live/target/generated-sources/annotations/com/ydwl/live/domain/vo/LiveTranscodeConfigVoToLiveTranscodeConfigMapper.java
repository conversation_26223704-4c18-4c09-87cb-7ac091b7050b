package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeConfig;
import com.ydwl.live.domain.LiveTranscodeConfigToLiveTranscodeConfigVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTranscodeConfigToLiveTranscodeConfigVoMapper.class},
    imports = {}
)
public interface LiveTranscodeConfigVoToLiveTranscodeConfigMapper extends BaseMapper<LiveTranscodeConfigVo, LiveTranscodeConfig> {
}
