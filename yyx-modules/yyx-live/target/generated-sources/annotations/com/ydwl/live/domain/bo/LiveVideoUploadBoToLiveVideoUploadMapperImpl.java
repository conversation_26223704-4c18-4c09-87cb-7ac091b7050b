package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveVideoUpload;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveVideoUploadBoToLiveVideoUploadMapperImpl implements LiveVideoUploadBoToLiveVideoUploadMapper {

    @Override
    public LiveVideoUpload convert(LiveVideoUploadBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveVideoUpload liveVideoUpload = new LiveVideoUpload();

        liveVideoUpload.setSearchValue( arg0.getSearchValue() );
        liveVideoUpload.setCreateDept( arg0.getCreateDept() );
        liveVideoUpload.setCreateBy( arg0.getCreateBy() );
        liveVideoUpload.setCreateTime( arg0.getCreateTime() );
        liveVideoUpload.setUpdateBy( arg0.getUpdateBy() );
        liveVideoUpload.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveVideoUpload.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveVideoUpload.setId( arg0.getId() );
        liveVideoUpload.setLiveId( arg0.getLiveId() );
        liveVideoUpload.setFileName( arg0.getFileName() );
        liveVideoUpload.setFileSizeBytes( arg0.getFileSizeBytes() );
        liveVideoUpload.setFileType( arg0.getFileType() );
        liveVideoUpload.setOssUrl( arg0.getOssUrl() );
        liveVideoUpload.setFileMd5( arg0.getFileMd5() );
        liveVideoUpload.setVideoDurationSeconds( arg0.getVideoDurationSeconds() );
        liveVideoUpload.setVideoResolution( arg0.getVideoResolution() );
        liveVideoUpload.setVideoBitrateKbps( arg0.getVideoBitrateKbps() );
        liveVideoUpload.setFrameRate( arg0.getFrameRate() );
        liveVideoUpload.setVideoCodec( arg0.getVideoCodec() );
        liveVideoUpload.setAudioCodec( arg0.getAudioCodec() );
        liveVideoUpload.setAspectRatio( arg0.getAspectRatio() );
        liveVideoUpload.setCreatedDate( arg0.getCreatedDate() );
        liveVideoUpload.setLastModifiedDate( arg0.getLastModifiedDate() );
        liveVideoUpload.setUploadStatus( arg0.getUploadStatus() );
        liveVideoUpload.setUploadProgressPercent( arg0.getUploadProgressPercent() );
        liveVideoUpload.setUploadCompleteTime( arg0.getUploadCompleteTime() );
        liveVideoUpload.setErrorMessage( arg0.getErrorMessage() );

        return liveVideoUpload;
    }

    @Override
    public LiveVideoUpload convert(LiveVideoUploadBo arg0, LiveVideoUpload arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setFileName( arg0.getFileName() );
        arg1.setFileSizeBytes( arg0.getFileSizeBytes() );
        arg1.setFileType( arg0.getFileType() );
        arg1.setOssUrl( arg0.getOssUrl() );
        arg1.setFileMd5( arg0.getFileMd5() );
        arg1.setVideoDurationSeconds( arg0.getVideoDurationSeconds() );
        arg1.setVideoResolution( arg0.getVideoResolution() );
        arg1.setVideoBitrateKbps( arg0.getVideoBitrateKbps() );
        arg1.setFrameRate( arg0.getFrameRate() );
        arg1.setVideoCodec( arg0.getVideoCodec() );
        arg1.setAudioCodec( arg0.getAudioCodec() );
        arg1.setAspectRatio( arg0.getAspectRatio() );
        arg1.setCreatedDate( arg0.getCreatedDate() );
        arg1.setLastModifiedDate( arg0.getLastModifiedDate() );
        arg1.setUploadStatus( arg0.getUploadStatus() );
        arg1.setUploadProgressPercent( arg0.getUploadProgressPercent() );
        arg1.setUploadCompleteTime( arg0.getUploadCompleteTime() );
        arg1.setErrorMessage( arg0.getErrorMessage() );

        return arg1;
    }
}
