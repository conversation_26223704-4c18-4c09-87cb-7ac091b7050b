package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveCategoryBoToLiveCategoryMapper;
import com.ydwl.live.domain.vo.LiveCategoryVo;
import com.ydwl.live.domain.vo.LiveCategoryVoToLiveCategoryMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveCategoryBoToLiveCategoryMapper.class,LiveCategoryVoToLiveCategoryMapper.class},
    imports = {}
)
public interface LiveCategoryToLiveCategoryVoMapper extends BaseMapper<LiveCategory, LiveCategoryVo> {
}
