package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveUserWallet;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveUserWalletVoToLiveUserWalletMapperImpl implements LiveUserWalletVoToLiveUserWalletMapper {

    @Override
    public LiveUserWallet convert(LiveUserWalletVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveUserWallet liveUserWallet = new LiveUserWallet();

        liveUserWallet.setId( arg0.getId() );
        liveUserWallet.setUserId( arg0.getUserId() );
        liveUserWallet.setBalance( arg0.getBalance() );
        liveUserWallet.setTotalRecharge( arg0.getTotalRecharge() );
        liveUserWallet.setTotalConsume( arg0.getTotalConsume() );

        return liveUserWallet;
    }

    @Override
    public LiveUserWallet convert(LiveUserWalletVo arg0, LiveUserWallet arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBalance( arg0.getBalance() );
        arg1.setTotalRecharge( arg0.getTotalRecharge() );
        arg1.setTotalConsume( arg0.getTotalConsume() );

        return arg1;
    }
}
