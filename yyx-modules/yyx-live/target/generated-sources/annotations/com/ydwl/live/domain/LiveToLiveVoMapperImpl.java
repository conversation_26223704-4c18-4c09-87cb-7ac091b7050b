package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveToLiveVoMapperImpl implements LiveToLiveVoMapper {

    @Override
    public LiveVo convert(Live arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveVo liveVo = new LiveVo();

        liveVo.setId( arg0.getId() );
        liveVo.setCategoryId( arg0.getCategoryId() );
        liveVo.setTitle( arg0.getTitle() );
        liveVo.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveVo.setPlanStartTime( arg0.getPlanStartTime() );
        liveVo.setActualStartTime( arg0.getActualStartTime() );
        liveVo.setActualEndTime( arg0.getActualEndTime() );
        liveVo.setDurationMinutes( arg0.getDurationMinutes() );
        liveVo.setStatus( arg0.getStatus() );
        liveVo.setStreamId( arg0.getStreamId() );
        liveVo.setSettingSignupRequired( arg0.getSettingSignupRequired() );
        liveVo.setSettingReplayEnabled( arg0.getSettingReplayEnabled() );
        liveVo.setSettingAutoRecord( arg0.getSettingAutoRecord() );
        liveVo.setSettingChatEnabled( arg0.getSettingChatEnabled() );
        liveVo.setSettingChatDelay( arg0.getSettingChatDelay() );
        liveVo.setSettingGiftEnabled( arg0.getSettingGiftEnabled() );
        liveVo.setSettingDefaultQuality( arg0.getSettingDefaultQuality() );
        liveVo.setSettingAccessLevel( arg0.getSettingAccessLevel() );
        liveVo.setDescription( arg0.getDescription() );
        liveVo.setTagList( arg0.getTagList() );
        liveVo.setRecordVideoUrl( arg0.getRecordVideoUrl() );
        liveVo.setViewCount( arg0.getViewCount() );
        liveVo.setLikeCount( arg0.getLikeCount() );
        liveVo.setShareCount( arg0.getShareCount() );
        liveVo.setMaxOnlineCount( arg0.getMaxOnlineCount() );
        liveVo.setIsFeatured( arg0.getIsFeatured() );
        liveVo.setHasReplay( arg0.getHasReplay() );

        return liveVo;
    }

    @Override
    public LiveVo convert(Live arg0, LiveVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setCategoryId( arg0.getCategoryId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setPlanStartTime( arg0.getPlanStartTime() );
        arg1.setActualStartTime( arg0.getActualStartTime() );
        arg1.setActualEndTime( arg0.getActualEndTime() );
        arg1.setDurationMinutes( arg0.getDurationMinutes() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setStreamId( arg0.getStreamId() );
        arg1.setSettingSignupRequired( arg0.getSettingSignupRequired() );
        arg1.setSettingReplayEnabled( arg0.getSettingReplayEnabled() );
        arg1.setSettingAutoRecord( arg0.getSettingAutoRecord() );
        arg1.setSettingChatEnabled( arg0.getSettingChatEnabled() );
        arg1.setSettingChatDelay( arg0.getSettingChatDelay() );
        arg1.setSettingGiftEnabled( arg0.getSettingGiftEnabled() );
        arg1.setSettingDefaultQuality( arg0.getSettingDefaultQuality() );
        arg1.setSettingAccessLevel( arg0.getSettingAccessLevel() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setTagList( arg0.getTagList() );
        arg1.setRecordVideoUrl( arg0.getRecordVideoUrl() );
        arg1.setViewCount( arg0.getViewCount() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setShareCount( arg0.getShareCount() );
        arg1.setMaxOnlineCount( arg0.getMaxOnlineCount() );
        arg1.setIsFeatured( arg0.getIsFeatured() );
        arg1.setHasReplay( arg0.getHasReplay() );

        return arg1;
    }
}
