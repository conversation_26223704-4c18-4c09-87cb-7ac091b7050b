package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveArticle;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveArticleBoToLiveArticleMapperImpl implements LiveArticleBoToLiveArticleMapper {

    @Override
    public LiveArticle convert(LiveArticleBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveArticle liveArticle = new LiveArticle();

        liveArticle.setSearchValue( arg0.getSearchValue() );
        liveArticle.setCreateDept( arg0.getCreateDept() );
        liveArticle.setCreateBy( arg0.getCreateBy() );
        liveArticle.setCreateTime( arg0.getCreateTime() );
        liveArticle.setUpdateBy( arg0.getUpdateBy() );
        liveArticle.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveArticle.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveArticle.setId( arg0.getId() );
        liveArticle.setRelatedId( arg0.getRelatedId() );
        liveArticle.setTitle( arg0.getTitle() );
        liveArticle.setContent( arg0.getContent() );
        liveArticle.setPublishStatus( arg0.getPublishStatus() );
        liveArticle.setRelatedType( arg0.getRelatedType() );
        liveArticle.setContentFormat( arg0.getContentFormat() );
        liveArticle.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveArticle.setSummary( arg0.getSummary() );
        liveArticle.setLastEditTime( arg0.getLastEditTime() );
        liveArticle.setIsFeatured( arg0.getIsFeatured() );
        liveArticle.setIsCommentAllowed( arg0.getIsCommentAllowed() );
        liveArticle.setViewCount( arg0.getViewCount() );
        liveArticle.setLikeCount( arg0.getLikeCount() );
        liveArticle.setShareCount( arg0.getShareCount() );

        return liveArticle;
    }

    @Override
    public LiveArticle convert(LiveArticleBo arg0, LiveArticle arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setRelatedId( arg0.getRelatedId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setContent( arg0.getContent() );
        arg1.setPublishStatus( arg0.getPublishStatus() );
        arg1.setRelatedType( arg0.getRelatedType() );
        arg1.setContentFormat( arg0.getContentFormat() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setLastEditTime( arg0.getLastEditTime() );
        arg1.setIsFeatured( arg0.getIsFeatured() );
        arg1.setIsCommentAllowed( arg0.getIsCommentAllowed() );
        arg1.setViewCount( arg0.getViewCount() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setShareCount( arg0.getShareCount() );

        return arg1;
    }
}
