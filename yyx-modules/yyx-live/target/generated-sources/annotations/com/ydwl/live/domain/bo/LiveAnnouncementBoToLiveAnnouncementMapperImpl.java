package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveAnnouncement;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveAnnouncementBoToLiveAnnouncementMapperImpl implements LiveAnnouncementBoToLiveAnnouncementMapper {

    @Override
    public LiveAnnouncement convert(LiveAnnouncementBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveAnnouncement liveAnnouncement = new LiveAnnouncement();

        liveAnnouncement.setSearchValue( arg0.getSearchValue() );
        liveAnnouncement.setCreateDept( arg0.getCreateDept() );
        liveAnnouncement.setCreateBy( arg0.getCreateBy() );
        liveAnnouncement.setCreateTime( arg0.getCreateTime() );
        liveAnnouncement.setUpdateBy( arg0.getUpdateBy() );
        liveAnnouncement.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveAnnouncement.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveAnnouncement.setId( arg0.getId() );
        liveAnnouncement.setLiveId( arg0.getLiveId() );
        liveAnnouncement.setContent( arg0.getContent() );
        liveAnnouncement.setStartTime( arg0.getStartTime() );
        liveAnnouncement.setEndTime( arg0.getEndTime() );
        liveAnnouncement.setAnnouncementStatus( arg0.getAnnouncementStatus() );

        return liveAnnouncement;
    }

    @Override
    public LiveAnnouncement convert(LiveAnnouncementBo arg0, LiveAnnouncement arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setContent( arg0.getContent() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setAnnouncementStatus( arg0.getAnnouncementStatus() );

        return arg1;
    }
}
