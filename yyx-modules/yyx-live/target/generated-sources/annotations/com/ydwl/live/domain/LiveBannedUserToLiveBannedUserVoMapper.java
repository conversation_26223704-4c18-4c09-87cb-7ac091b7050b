package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveBannedUserBoToLiveBannedUserMapper;
import com.ydwl.live.domain.vo.LiveBannedUserVo;
import com.ydwl.live.domain.vo.LiveBannedUserVoToLiveBannedUserMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveBannedUserVoToLiveBannedUserMapper.class,LiveBannedUserBoToLiveBannedUserMapper.class},
    imports = {}
)
public interface LiveBannedUserToLiveBannedUserVoMapper extends BaseMapper<LiveBannedUser, LiveBannedUserVo> {
}
