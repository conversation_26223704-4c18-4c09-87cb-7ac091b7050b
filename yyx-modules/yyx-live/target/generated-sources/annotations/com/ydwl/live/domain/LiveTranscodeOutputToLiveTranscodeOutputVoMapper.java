package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveTranscodeOutputBoToLiveTranscodeOutputMapper;
import com.ydwl.live.domain.vo.LiveTranscodeOutputVo;
import com.ydwl.live.domain.vo.LiveTranscodeOutputVoToLiveTranscodeOutputMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTranscodeOutputBoToLiveTranscodeOutputMapper.class,LiveTranscodeOutputVoToLiveTranscodeOutputMapper.class},
    imports = {}
)
public interface LiveTranscodeOutputToLiveTranscodeOutputVoMapper extends BaseMapper<LiveTranscodeOutput, LiveTranscodeOutputVo> {
}
