package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveVideoPlayRecord;
import com.ydwl.live.domain.LiveVideoPlayRecordToLiveVideoPlayRecordVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveVideoPlayRecordToLiveVideoPlayRecordVoMapper.class},
    imports = {}
)
public interface LiveVideoPlayRecordVoToLiveVideoPlayRecordMapper extends BaseMapper<LiveVideoPlayRecordVo, LiveVideoPlayRecord> {
}
