package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveUser;
import com.ydwl.live.domain.LiveUserToLiveUserVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveUserToLiveUserVoMapper.class},
    imports = {}
)
public interface LiveUserVoToLiveUserMapper extends BaseMapper<LiveUserVo, LiveUser> {
}
