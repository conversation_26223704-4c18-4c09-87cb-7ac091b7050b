package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveTranscodeOutput;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeOutputBoToLiveTranscodeOutputMapperImpl implements LiveTranscodeOutputBoToLiveTranscodeOutputMapper {

    @Override
    public LiveTranscodeOutput convert(LiveTranscodeOutputBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeOutput liveTranscodeOutput = new LiveTranscodeOutput();

        liveTranscodeOutput.setSearchValue( arg0.getSearchValue() );
        liveTranscodeOutput.setCreateDept( arg0.getCreateDept() );
        liveTranscodeOutput.setCreateBy( arg0.getCreateBy() );
        liveTranscodeOutput.setCreateTime( arg0.getCreateTime() );
        liveTranscodeOutput.setUpdateBy( arg0.getUpdateBy() );
        liveTranscodeOutput.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveTranscodeOutput.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveTranscodeOutput.setId( arg0.getId() );
        liveTranscodeOutput.setTaskId( arg0.getTaskId() );
        liveTranscodeOutput.setDefinition( arg0.getDefinition() );
        liveTranscodeOutput.setResolution( arg0.getResolution() );
        liveTranscodeOutput.setFormat( arg0.getFormat() );
        liveTranscodeOutput.setUrl( arg0.getUrl() );
        liveTranscodeOutput.setSegmentTime( arg0.getSegmentTime() );
        liveTranscodeOutput.setFileSize( arg0.getFileSize() );
        liveTranscodeOutput.setBitRate( arg0.getBitRate() );
        liveTranscodeOutput.setDuration( arg0.getDuration() );

        return liveTranscodeOutput;
    }

    @Override
    public LiveTranscodeOutput convert(LiveTranscodeOutputBo arg0, LiveTranscodeOutput arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setDefinition( arg0.getDefinition() );
        arg1.setResolution( arg0.getResolution() );
        arg1.setFormat( arg0.getFormat() );
        arg1.setUrl( arg0.getUrl() );
        arg1.setSegmentTime( arg0.getSegmentTime() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setBitRate( arg0.getBitRate() );
        arg1.setDuration( arg0.getDuration() );

        return arg1;
    }
}
