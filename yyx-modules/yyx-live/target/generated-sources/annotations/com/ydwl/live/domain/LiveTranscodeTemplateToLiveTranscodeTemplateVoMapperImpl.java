package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveTranscodeTemplateVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeTemplateToLiveTranscodeTemplateVoMapperImpl implements LiveTranscodeTemplateToLiveTranscodeTemplateVoMapper {

    @Override
    public LiveTranscodeTemplateVo convert(LiveTranscodeTemplate arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeTemplateVo liveTranscodeTemplateVo = new LiveTranscodeTemplateVo();

        liveTranscodeTemplateVo.setId( arg0.getId() );
        liveTranscodeTemplateVo.setTemplateName( arg0.getTemplateName() );
        liveTranscodeTemplateVo.setTemplateType( arg0.getTemplateType() );
        liveTranscodeTemplateVo.setDefinition( arg0.getDefinition() );
        liveTranscodeTemplateVo.setResolution( arg0.getResolution() );
        liveTranscodeTemplateVo.setVideoBitrate( arg0.getVideoBitrate() );
        liveTranscodeTemplateVo.setAudioBitrate( arg0.getAudioBitrate() );
        liveTranscodeTemplateVo.setFps( arg0.getFps() );
        liveTranscodeTemplateVo.setFormat( arg0.getFormat() );
        liveTranscodeTemplateVo.setCodec( arg0.getCodec() );
        liveTranscodeTemplateVo.setExternalId( arg0.getExternalId() );
        liveTranscodeTemplateVo.setStatus( arg0.getStatus() );
        liveTranscodeTemplateVo.setIsDefault( arg0.getIsDefault() );

        return liveTranscodeTemplateVo;
    }

    @Override
    public LiveTranscodeTemplateVo convert(LiveTranscodeTemplate arg0, LiveTranscodeTemplateVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTemplateName( arg0.getTemplateName() );
        arg1.setTemplateType( arg0.getTemplateType() );
        arg1.setDefinition( arg0.getDefinition() );
        arg1.setResolution( arg0.getResolution() );
        arg1.setVideoBitrate( arg0.getVideoBitrate() );
        arg1.setAudioBitrate( arg0.getAudioBitrate() );
        arg1.setFps( arg0.getFps() );
        arg1.setFormat( arg0.getFormat() );
        arg1.setCodec( arg0.getCodec() );
        arg1.setExternalId( arg0.getExternalId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setIsDefault( arg0.getIsDefault() );

        return arg1;
    }
}
