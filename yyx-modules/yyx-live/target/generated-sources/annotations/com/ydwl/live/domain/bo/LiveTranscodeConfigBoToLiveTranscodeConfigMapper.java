package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveTranscodeConfig;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {},
    imports = {}
)
public interface LiveTranscodeConfigBoToLiveTranscodeConfigMapper extends BaseMapper<LiveTranscodeConfigBo, LiveTranscodeConfig> {
}
