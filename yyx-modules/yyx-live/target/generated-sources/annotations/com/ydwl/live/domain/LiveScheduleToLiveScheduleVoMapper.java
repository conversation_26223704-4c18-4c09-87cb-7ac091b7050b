package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveScheduleBoToLiveScheduleMapper;
import com.ydwl.live.domain.vo.LiveScheduleVo;
import com.ydwl.live.domain.vo.LiveScheduleVoToLiveScheduleMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveScheduleVoToLiveScheduleMapper.class,LiveScheduleBoToLiveScheduleMapper.class},
    imports = {}
)
public interface LiveScheduleToLiveScheduleVoMapper extends BaseMapper<LiveSchedule, LiveScheduleVo> {
}
