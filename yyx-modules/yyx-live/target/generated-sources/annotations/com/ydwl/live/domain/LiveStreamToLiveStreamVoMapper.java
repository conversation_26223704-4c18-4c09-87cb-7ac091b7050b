package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveStreamBoToLiveStreamMapper;
import com.ydwl.live.domain.vo.GetStreamVoToLiveStreamMapper;
import com.ydwl.live.domain.vo.LiveStreamVo;
import com.ydwl.live.domain.vo.LiveStreamVoToLiveStreamMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {GetStreamVoToLiveStreamMapper.class,LiveStreamBoToLiveStreamMapper.class,LiveStreamVoToLiveStreamMapper.class,LiveStreamToGetStreamVoMapper.class},
    imports = {}
)
public interface LiveStreamToLiveStreamVoMapper extends BaseMapper<LiveStream, LiveStreamVo> {
}
