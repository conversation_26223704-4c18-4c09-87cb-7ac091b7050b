package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveStreamBoToLiveStreamMapper;
import com.ydwl.live.domain.vo.GetStreamVo;
import com.ydwl.live.domain.vo.GetStreamVoToLiveStreamMapper;
import com.ydwl.live.domain.vo.LiveStreamVoToLiveStreamMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {GetStreamVoToLiveStreamMapper.class,LiveStreamBoToLiveStreamMapper.class,LiveStreamVoToLiveStreamMapper.class,LiveStreamToLiveStreamVoMapper.class},
    imports = {}
)
public interface LiveStreamToGetStreamVoMapper extends BaseMapper<LiveStream, GetStreamVo> {
}
