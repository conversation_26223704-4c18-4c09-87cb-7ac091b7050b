package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveTranscodeConfigVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeConfigToLiveTranscodeConfigVoMapperImpl implements LiveTranscodeConfigToLiveTranscodeConfigVoMapper {

    @Override
    public LiveTranscodeConfigVo convert(LiveTranscodeConfig arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeConfigVo liveTranscodeConfigVo = new LiveTranscodeConfigVo();

        liveTranscodeConfigVo.setId( arg0.getId() );
        liveTranscodeConfigVo.setKey( arg0.getKey() );
        liveTranscodeConfigVo.setValue( arg0.getValue() );
        liveTranscodeConfigVo.setType( arg0.getType() );
        liveTranscodeConfigVo.setDescription( arg0.getDescription() );

        return liveTranscodeConfigVo;
    }

    @Override
    public LiveTranscodeConfigVo convert(LiveTranscodeConfig arg0, LiveTranscodeConfigVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setKey( arg0.getKey() );
        arg1.setValue( arg0.getValue() );
        arg1.setType( arg0.getType() );
        arg1.setDescription( arg0.getDescription() );

        return arg1;
    }
}
