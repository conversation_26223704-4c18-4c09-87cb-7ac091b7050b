package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveTranscodeTaskBoToLiveTranscodeTaskMapper;
import com.ydwl.live.domain.vo.LiveTranscodeOutputVoToLiveTranscodeOutputMapper;
import com.ydwl.live.domain.vo.LiveTranscodeTaskVo;
import com.ydwl.live.domain.vo.LiveTranscodeTaskVoToLiveTranscodeTaskMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTranscodeOutputVoToLiveTranscodeOutputMapper.class,LiveTranscodeOutputToLiveTranscodeOutputVoMapper.class,LiveTranscodeTaskVoToLiveTranscodeTaskMapper.class,LiveTranscodeTaskBoToLiveTranscodeTaskMapper.class},
    imports = {}
)
public interface LiveTranscodeTaskToLiveTranscodeTaskVoMapper extends BaseMapper<LiveTranscodeTask, LiveTranscodeTaskVo> {
}
