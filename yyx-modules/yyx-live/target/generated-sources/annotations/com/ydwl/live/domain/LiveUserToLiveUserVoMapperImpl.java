package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveUserVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveUserToLiveUserVoMapperImpl implements LiveUserToLiveUserVoMapper {

    @Override
    public LiveUserVo convert(LiveUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveUserVo liveUserVo = new LiveUserVo();

        liveUserVo.setId( arg0.getId() );
        liveUserVo.setUsername( arg0.getUsername() );
        liveUserVo.setPhone( arg0.getPhone() );
        liveUserVo.setOpenid( arg0.getOpenid() );
        liveUserVo.setAvatarUrl( arg0.getAvatarUrl() );
        liveUserVo.setNickname( arg0.getNickname() );
        liveUserVo.setEmail( arg0.getEmail() );
        liveUserVo.setUserStatus( arg0.getUserStatus() );
        liveUserVo.setBio( arg0.getBio() );
        liveUserVo.setGender( arg0.getGender() );
        liveUserVo.setBirthday( arg0.getBirthday() );
        liveUserVo.setLastLoginTime( arg0.getLastLoginTime() );
        liveUserVo.setLastLoginIp( arg0.getLastLoginIp() );
        liveUserVo.setNotificationSettings( arg0.getNotificationSettings() );
        liveUserVo.setUserStatistics( arg0.getUserStatistics() );

        return liveUserVo;
    }

    @Override
    public LiveUserVo convert(LiveUser arg0, LiveUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUsername( arg0.getUsername() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setOpenid( arg0.getOpenid() );
        arg1.setAvatarUrl( arg0.getAvatarUrl() );
        arg1.setNickname( arg0.getNickname() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setUserStatus( arg0.getUserStatus() );
        arg1.setBio( arg0.getBio() );
        arg1.setGender( arg0.getGender() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setLastLoginTime( arg0.getLastLoginTime() );
        arg1.setLastLoginIp( arg0.getLastLoginIp() );
        arg1.setNotificationSettings( arg0.getNotificationSettings() );
        arg1.setUserStatistics( arg0.getUserStatistics() );

        return arg1;
    }
}
