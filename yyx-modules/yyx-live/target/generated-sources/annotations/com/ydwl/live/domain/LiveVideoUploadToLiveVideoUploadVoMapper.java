package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveVideoUploadBoToLiveVideoUploadMapper;
import com.ydwl.live.domain.vo.LiveVideoUploadVo;
import com.ydwl.live.domain.vo.LiveVideoUploadVoToLiveVideoUploadMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveVideoUploadBoToLiveVideoUploadMapper.class,LiveVideoUploadVoToLiveVideoUploadMapper.class},
    imports = {}
)
public interface LiveVideoUploadToLiveVideoUploadVoMapper extends BaseMapper<LiveVideoUpload, LiveVideoUploadVo> {
}
