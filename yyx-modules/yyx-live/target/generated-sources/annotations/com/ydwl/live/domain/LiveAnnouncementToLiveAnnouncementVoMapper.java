package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveAnnouncementBoToLiveAnnouncementMapper;
import com.ydwl.live.domain.vo.LiveAnnouncementVo;
import com.ydwl.live.domain.vo.LiveAnnouncementVoToLiveAnnouncementMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveAnnouncementBoToLiveAnnouncementMapper.class,LiveAnnouncementVoToLiveAnnouncementMapper.class},
    imports = {}
)
public interface LiveAnnouncementToLiveAnnouncementVoMapper extends BaseMapper<LiveAnnouncement, LiveAnnouncementVo> {
}
