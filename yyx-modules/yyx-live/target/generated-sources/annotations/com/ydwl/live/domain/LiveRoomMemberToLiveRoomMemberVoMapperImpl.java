package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveRoomMemberVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveRoomMemberToLiveRoomMemberVoMapperImpl implements LiveRoomMemberToLiveRoomMemberVoMapper {

    @Override
    public LiveRoomMemberVo convert(LiveRoomMember arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveRoomMemberVo liveRoomMemberVo = new LiveRoomMemberVo();

        liveRoomMemberVo.setId( arg0.getId() );
        liveRoomMemberVo.setLiveId( arg0.getLiveId() );
        liveRoomMemberVo.setUserId( arg0.getUserId() );
        liveRoomMemberVo.setJoinTime( arg0.getJoinTime() );
        liveRoomMemberVo.setLeaveTime( arg0.getLeaveTime() );
        liveRoomMemberVo.setWatchDurationSeconds( arg0.getWatchDurationSeconds() );
        liveRoomMemberVo.setClientIp( arg0.getClientIp() );
        liveRoomMemberVo.setClientDevice( arg0.getClientDevice() );

        return liveRoomMemberVo;
    }

    @Override
    public LiveRoomMemberVo convert(LiveRoomMember arg0, LiveRoomMemberVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setJoinTime( arg0.getJoinTime() );
        arg1.setLeaveTime( arg0.getLeaveTime() );
        arg1.setWatchDurationSeconds( arg0.getWatchDurationSeconds() );
        arg1.setClientIp( arg0.getClientIp() );
        arg1.setClientDevice( arg0.getClientDevice() );

        return arg1;
    }
}
