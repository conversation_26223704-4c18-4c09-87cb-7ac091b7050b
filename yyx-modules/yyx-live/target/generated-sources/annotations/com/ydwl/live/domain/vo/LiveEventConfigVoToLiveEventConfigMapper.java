package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveEventConfig;
import com.ydwl.live.domain.LiveEventConfigToLiveEventConfigVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveEventConfigToLiveEventConfigVoMapper.class},
    imports = {}
)
public interface LiveEventConfigVoToLiveEventConfigMapper extends BaseMapper<LiveEventConfigVo, LiveEventConfig> {
}
