package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveTranscodeTemplateBoToLiveTranscodeTemplateMapper;
import com.ydwl.live.domain.vo.LiveTranscodeTemplateVo;
import com.ydwl.live.domain.vo.LiveTranscodeTemplateVoToLiveTranscodeTemplateMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTranscodeTemplateVoToLiveTranscodeTemplateMapper.class,LiveTranscodeTemplateBoToLiveTranscodeTemplateMapper.class},
    imports = {}
)
public interface LiveTranscodeTemplateToLiveTranscodeTemplateVoMapper extends BaseMapper<LiveTranscodeTemplate, LiveTranscodeTemplateVo> {
}
