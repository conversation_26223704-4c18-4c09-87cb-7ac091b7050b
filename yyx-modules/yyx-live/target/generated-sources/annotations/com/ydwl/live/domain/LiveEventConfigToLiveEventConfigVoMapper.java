package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveEventConfigBoToLiveEventConfigMapper;
import com.ydwl.live.domain.vo.LiveEventConfigVo;
import com.ydwl.live.domain.vo.LiveEventConfigVoToLiveEventConfigMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveEventConfigBoToLiveEventConfigMapper.class,LiveEventConfigVoToLiveEventConfigMapper.class},
    imports = {}
)
public interface LiveEventConfigToLiveEventConfigVoMapper extends BaseMapper<LiveEventConfig, LiveEventConfigVo> {
}
