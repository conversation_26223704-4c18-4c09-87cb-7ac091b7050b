package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveUserWalletVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveUserWalletToLiveUserWalletVoMapperImpl implements LiveUserWalletToLiveUserWalletVoMapper {

    @Override
    public LiveUserWalletVo convert(LiveUserWallet arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveUserWalletVo liveUserWalletVo = new LiveUserWalletVo();

        liveUserWalletVo.setId( arg0.getId() );
        liveUserWalletVo.setUserId( arg0.getUserId() );
        liveUserWalletVo.setBalance( arg0.getBalance() );
        liveUserWalletVo.setTotalRecharge( arg0.getTotalRecharge() );
        liveUserWalletVo.setTotalConsume( arg0.getTotalConsume() );

        return liveUserWalletVo;
    }

    @Override
    public LiveUserWalletVo convert(LiveUserWallet arg0, LiveUserWalletVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBalance( arg0.getBalance() );
        arg1.setTotalRecharge( arg0.getTotalRecharge() );
        arg1.setTotalConsume( arg0.getTotalConsume() );

        return arg1;
    }
}
