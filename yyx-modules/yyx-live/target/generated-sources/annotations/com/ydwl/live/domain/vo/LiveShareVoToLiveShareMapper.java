package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveShare;
import com.ydwl.live.domain.LiveShareToLiveShareVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveShareToLiveShareVoMapper.class},
    imports = {}
)
public interface LiveShareVoToLiveShareMapper extends BaseMapper<LiveShareVo, LiveShare> {
}
