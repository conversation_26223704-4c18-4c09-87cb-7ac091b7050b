package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveOrderVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveOrderToLiveOrderVoMapperImpl implements LiveOrderToLiveOrderVoMapper {

    @Override
    public LiveOrderVo convert(LiveOrder arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveOrderVo liveOrderVo = new LiveOrderVo();

        liveOrderVo.setId( arg0.getId() );
        liveOrderVo.setOrderNo( arg0.getOrderNo() );
        liveOrderVo.setUserId( arg0.getUserId() );
        liveOrderVo.setProductId( arg0.getProductId() );
        liveOrderVo.setOrderType( arg0.getOrderType() );
        liveOrderVo.setAmount( arg0.getAmount() );
        liveOrderVo.setCoinAmount( arg0.getCoinAmount() );
        liveOrderVo.setPaymentMethod( arg0.getPaymentMethod() );
        liveOrderVo.setPaymentTime( arg0.getPaymentTime() );
        liveOrderVo.setTransactionId( arg0.getTransactionId() );
        liveOrderVo.setStatus( arg0.getStatus() );

        return liveOrderVo;
    }

    @Override
    public LiveOrderVo convert(LiveOrder arg0, LiveOrderVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setOrderNo( arg0.getOrderNo() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setProductId( arg0.getProductId() );
        arg1.setOrderType( arg0.getOrderType() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoinAmount( arg0.getCoinAmount() );
        arg1.setPaymentMethod( arg0.getPaymentMethod() );
        arg1.setPaymentTime( arg0.getPaymentTime() );
        arg1.setTransactionId( arg0.getTransactionId() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
