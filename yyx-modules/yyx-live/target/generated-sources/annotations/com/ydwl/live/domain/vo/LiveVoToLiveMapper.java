package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.Live;
import com.ydwl.live.domain.LiveToLiveVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveToLiveVoMapper.class},
    imports = {}
)
public interface LiveVoToLiveMapper extends BaseMapper<LiveVo, Live> {
}
