package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveVideoPlayRecord;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveVideoPlayRecordBoToLiveVideoPlayRecordMapperImpl implements LiveVideoPlayRecordBoToLiveVideoPlayRecordMapper {

    @Override
    public LiveVideoPlayRecord convert(LiveVideoPlayRecordBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveVideoPlayRecord liveVideoPlayRecord = new LiveVideoPlayRecord();

        liveVideoPlayRecord.setSearchValue( arg0.getSearchValue() );
        liveVideoPlayRecord.setCreateDept( arg0.getCreateDept() );
        liveVideoPlayRecord.setCreateBy( arg0.getCreateBy() );
        liveVideoPlayRecord.setCreateTime( arg0.getCreateTime() );
        liveVideoPlayRecord.setUpdateBy( arg0.getUpdateBy() );
        liveVideoPlayRecord.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveVideoPlayRecord.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveVideoPlayRecord.setId( arg0.getId() );
        liveVideoPlayRecord.setLiveId( arg0.getLiveId() );
        liveVideoPlayRecord.setUserId( arg0.getUserId() );
        liveVideoPlayRecord.setPlayTime( arg0.getPlayTime() );
        liveVideoPlayRecord.setPlayDurationSeconds( arg0.getPlayDurationSeconds() );
        liveVideoPlayRecord.setLastPositionSeconds( arg0.getLastPositionSeconds() );
        liveVideoPlayRecord.setIsFinished( arg0.getIsFinished() );
        liveVideoPlayRecord.setDeviceType( arg0.getDeviceType() );
        liveVideoPlayRecord.setIpAddress( arg0.getIpAddress() );
        liveVideoPlayRecord.setVideoQuality( arg0.getVideoQuality() );

        return liveVideoPlayRecord;
    }

    @Override
    public LiveVideoPlayRecord convert(LiveVideoPlayRecordBo arg0, LiveVideoPlayRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setPlayTime( arg0.getPlayTime() );
        arg1.setPlayDurationSeconds( arg0.getPlayDurationSeconds() );
        arg1.setLastPositionSeconds( arg0.getLastPositionSeconds() );
        arg1.setIsFinished( arg0.getIsFinished() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setIpAddress( arg0.getIpAddress() );
        arg1.setVideoQuality( arg0.getVideoQuality() );

        return arg1;
    }
}
