package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveVideoPlayRecordBoToLiveVideoPlayRecordMapper;
import com.ydwl.live.domain.vo.LiveVideoPlayRecordVo;
import com.ydwl.live.domain.vo.LiveVideoPlayRecordVoToLiveVideoPlayRecordMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveVideoPlayRecordVoToLiveVideoPlayRecordMapper.class,LiveVideoPlayRecordBoToLiveVideoPlayRecordMapper.class},
    imports = {}
)
public interface LiveVideoPlayRecordToLiveVideoPlayRecordVoMapper extends BaseMapper<LiveVideoPlayRecord, LiveVideoPlayRecordVo> {
}
