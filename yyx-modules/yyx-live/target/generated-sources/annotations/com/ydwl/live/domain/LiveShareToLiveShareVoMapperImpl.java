package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveShareVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveShareToLiveShareVoMapperImpl implements LiveShareToLiveShareVoMapper {

    @Override
    public LiveShareVo convert(LiveShare arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveShareVo liveShareVo = new LiveShareVo();

        liveShareVo.setId( arg0.getId() );
        liveShareVo.setLiveId( arg0.getLiveId() );
        liveShareVo.setUserId( arg0.getUserId() );
        liveShareVo.setSharePlatform( arg0.getSharePlatform() );

        return liveShareVo;
    }

    @Override
    public LiveShareVo convert(LiveShare arg0, LiveShareVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setSharePlatform( arg0.getSharePlatform() );

        return arg1;
    }
}
