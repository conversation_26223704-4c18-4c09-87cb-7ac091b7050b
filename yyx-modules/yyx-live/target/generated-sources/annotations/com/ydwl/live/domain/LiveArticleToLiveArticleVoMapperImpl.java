package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveArticleVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveArticleToLiveArticleVoMapperImpl implements LiveArticleToLiveArticleVoMapper {

    @Override
    public LiveArticleVo convert(LiveArticle arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveArticleVo liveArticleVo = new LiveArticleVo();

        liveArticleVo.setId( arg0.getId() );
        liveArticleVo.setRelatedId( arg0.getRelatedId() );
        liveArticleVo.setTitle( arg0.getTitle() );
        liveArticleVo.setContent( arg0.getContent() );
        liveArticleVo.setPublishStatus( arg0.getPublishStatus() );
        liveArticleVo.setRelatedType( arg0.getRelatedType() );
        liveArticleVo.setContentFormat( arg0.getContentFormat() );
        liveArticleVo.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveArticleVo.setSummary( arg0.getSummary() );
        liveArticleVo.setLastEditTime( arg0.getLastEditTime() );
        liveArticleVo.setIsFeatured( arg0.getIsFeatured() );
        liveArticleVo.setIsCommentAllowed( arg0.getIsCommentAllowed() );
        liveArticleVo.setViewCount( arg0.getViewCount() );
        liveArticleVo.setLikeCount( arg0.getLikeCount() );
        liveArticleVo.setShareCount( arg0.getShareCount() );

        return liveArticleVo;
    }

    @Override
    public LiveArticleVo convert(LiveArticle arg0, LiveArticleVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setRelatedId( arg0.getRelatedId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setContent( arg0.getContent() );
        arg1.setPublishStatus( arg0.getPublishStatus() );
        arg1.setRelatedType( arg0.getRelatedType() );
        arg1.setContentFormat( arg0.getContentFormat() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setLastEditTime( arg0.getLastEditTime() );
        arg1.setIsFeatured( arg0.getIsFeatured() );
        arg1.setIsCommentAllowed( arg0.getIsCommentAllowed() );
        arg1.setViewCount( arg0.getViewCount() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setShareCount( arg0.getShareCount() );

        return arg1;
    }
}
