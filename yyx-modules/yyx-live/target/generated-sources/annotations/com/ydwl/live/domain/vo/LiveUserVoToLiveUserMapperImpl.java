package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveUser;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveUserVoToLiveUserMapperImpl implements LiveUserVoToLiveUserMapper {

    @Override
    public LiveUser convert(LiveUserVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveUser liveUser = new LiveUser();

        liveUser.setId( arg0.getId() );
        liveUser.setUsername( arg0.getUsername() );
        liveUser.setPhone( arg0.getPhone() );
        liveUser.setOpenid( arg0.getOpenid() );
        liveUser.setAvatarUrl( arg0.getAvatarUrl() );
        liveUser.setNickname( arg0.getNickname() );
        liveUser.setEmail( arg0.getEmail() );
        liveUser.setUserStatus( arg0.getUserStatus() );
        liveUser.setBio( arg0.getBio() );
        liveUser.setGender( arg0.getGender() );
        liveUser.setBirthday( arg0.getBirthday() );
        liveUser.setLastLoginTime( arg0.getLastLoginTime() );
        liveUser.setLastLoginIp( arg0.getLastLoginIp() );
        liveUser.setNotificationSettings( arg0.getNotificationSettings() );
        liveUser.setUserStatistics( arg0.getUserStatistics() );

        return liveUser;
    }

    @Override
    public LiveUser convert(LiveUserVo arg0, LiveUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUsername( arg0.getUsername() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setOpenid( arg0.getOpenid() );
        arg1.setAvatarUrl( arg0.getAvatarUrl() );
        arg1.setNickname( arg0.getNickname() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setUserStatus( arg0.getUserStatus() );
        arg1.setBio( arg0.getBio() );
        arg1.setGender( arg0.getGender() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setLastLoginTime( arg0.getLastLoginTime() );
        arg1.setLastLoginIp( arg0.getLastLoginIp() );
        arg1.setNotificationSettings( arg0.getNotificationSettings() );
        arg1.setUserStatistics( arg0.getUserStatistics() );

        return arg1;
    }
}
