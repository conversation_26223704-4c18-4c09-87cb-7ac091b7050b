package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveTransactionVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTransactionToLiveTransactionVoMapperImpl implements LiveTransactionToLiveTransactionVoMapper {

    @Override
    public LiveTransactionVo convert(LiveTransaction arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTransactionVo liveTransactionVo = new LiveTransactionVo();

        liveTransactionVo.setId( arg0.getId() );
        liveTransactionVo.setUserId( arg0.getUserId() );
        liveTransactionVo.setRelatedId( arg0.getRelatedId() );
        liveTransactionVo.setType( arg0.getType() );
        liveTransactionVo.setAmount( arg0.getAmount() );
        liveTransactionVo.setBalance( arg0.getBalance() );
        liveTransactionVo.setDetail( arg0.getDetail() );
        liveTransactionVo.setTargetType( arg0.getTargetType() );
        liveTransactionVo.setTargetId( arg0.getTargetId() );

        return liveTransactionVo;
    }

    @Override
    public LiveTransactionVo convert(LiveTransaction arg0, LiveTransactionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setRelatedId( arg0.getRelatedId() );
        arg1.setType( arg0.getType() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setBalance( arg0.getBalance() );
        arg1.setDetail( arg0.getDetail() );
        arg1.setTargetType( arg0.getTargetType() );
        arg1.setTargetId( arg0.getTargetId() );

        return arg1;
    }
}
