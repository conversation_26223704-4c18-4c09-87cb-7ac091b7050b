package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveStream;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class GetStreamVoToLiveStreamMapperImpl implements GetStreamVoToLiveStreamMapper {

    @Override
    public LiveStream convert(GetStreamVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveStream liveStream = new LiveStream();

        liveStream.setUpdateTime( arg0.getUpdateTime() );
        liveStream.setId( arg0.getId() );
        liveStream.setLiveId( arg0.getLiveId() );
        liveStream.setPushUrl( arg0.getPushUrl() );
        liveStream.setPushKey( arg0.getPushKey() );
        liveStream.setStreamStatus( arg0.getStreamStatus() );

        return liveStream;
    }

    @Override
    public LiveStream convert(GetStreamVo arg0, LiveStream arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setPushUrl( arg0.getPushUrl() );
        arg1.setPushKey( arg0.getPushKey() );
        arg1.setStreamStatus( arg0.getStreamStatus() );

        return arg1;
    }
}
