package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveEventConfig;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveEventConfigBoToLiveEventConfigMapperImpl implements LiveEventConfigBoToLiveEventConfigMapper {

    @Override
    public LiveEventConfig convert(LiveEventConfigBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveEventConfig liveEventConfig = new LiveEventConfig();

        liveEventConfig.setSearchValue( arg0.getSearchValue() );
        liveEventConfig.setCreateDept( arg0.getCreateDept() );
        liveEventConfig.setCreateBy( arg0.getCreateBy() );
        liveEventConfig.setCreateTime( arg0.getCreateTime() );
        liveEventConfig.setUpdateBy( arg0.getUpdateBy() );
        liveEventConfig.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveEventConfig.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveEventConfig.setId( arg0.getId() );
        liveEventConfig.setEventType( arg0.getEventType() );
        liveEventConfig.setEventId( arg0.getEventId() );
        liveEventConfig.setSignupStart( arg0.getSignupStart() );
        liveEventConfig.setSignupEnd( arg0.getSignupEnd() );
        liveEventConfig.setMaxParticipants( arg0.getMaxParticipants() );
        liveEventConfig.setSignupFields( arg0.getSignupFields() );

        return liveEventConfig;
    }

    @Override
    public LiveEventConfig convert(LiveEventConfigBo arg0, LiveEventConfig arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setEventType( arg0.getEventType() );
        arg1.setEventId( arg0.getEventId() );
        arg1.setSignupStart( arg0.getSignupStart() );
        arg1.setSignupEnd( arg0.getSignupEnd() );
        arg1.setMaxParticipants( arg0.getMaxParticipants() );
        arg1.setSignupFields( arg0.getSignupFields() );

        return arg1;
    }
}
