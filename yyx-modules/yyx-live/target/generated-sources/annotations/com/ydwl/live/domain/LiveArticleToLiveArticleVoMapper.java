package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveArticleBoToLiveArticleMapper;
import com.ydwl.live.domain.vo.LiveArticleVo;
import com.ydwl.live.domain.vo.LiveArticleVoToLiveArticleMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveArticleBoToLiveArticleMapper.class,LiveArticleVoToLiveArticleMapper.class},
    imports = {}
)
public interface LiveArticleToLiveArticleVoMapper extends BaseMapper<LiveArticle, LiveArticleVo> {
}
