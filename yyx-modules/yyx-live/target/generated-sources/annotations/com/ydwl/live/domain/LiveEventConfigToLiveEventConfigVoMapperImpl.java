package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveEventConfigVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveEventConfigToLiveEventConfigVoMapperImpl implements LiveEventConfigToLiveEventConfigVoMapper {

    @Override
    public LiveEventConfigVo convert(LiveEventConfig arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveEventConfigVo liveEventConfigVo = new LiveEventConfigVo();

        liveEventConfigVo.setId( arg0.getId() );
        liveEventConfigVo.setEventType( arg0.getEventType() );
        liveEventConfigVo.setEventId( arg0.getEventId() );
        liveEventConfigVo.setSignupStart( arg0.getSignupStart() );
        liveEventConfigVo.setSignupEnd( arg0.getSignupEnd() );
        liveEventConfigVo.setMaxParticipants( arg0.getMaxParticipants() );
        liveEventConfigVo.setSignupFields( arg0.getSignupFields() );

        return liveEventConfigVo;
    }

    @Override
    public LiveEventConfigVo convert(LiveEventConfig arg0, LiveEventConfigVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setEventType( arg0.getEventType() );
        arg1.setEventId( arg0.getEventId() );
        arg1.setSignupStart( arg0.getSignupStart() );
        arg1.setSignupEnd( arg0.getSignupEnd() );
        arg1.setMaxParticipants( arg0.getMaxParticipants() );
        arg1.setSignupFields( arg0.getSignupFields() );

        return arg1;
    }
}
