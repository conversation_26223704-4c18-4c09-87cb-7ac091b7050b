package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveRoomMemberBoToLiveRoomMemberMapper;
import com.ydwl.live.domain.vo.LiveRoomMemberVo;
import com.ydwl.live.domain.vo.LiveRoomMemberVoToLiveRoomMemberMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveRoomMemberBoToLiveRoomMemberMapper.class,LiveRoomMemberVoToLiveRoomMemberMapper.class},
    imports = {}
)
public interface LiveRoomMemberToLiveRoomMemberVoMapper extends BaseMapper<LiveRoomMember, LiveRoomMemberVo> {
}
