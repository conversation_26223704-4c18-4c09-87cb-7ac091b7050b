package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveUserWalletBoToLiveUserWalletMapper;
import com.ydwl.live.domain.vo.LiveUserWalletVo;
import com.ydwl.live.domain.vo.LiveUserWalletVoToLiveUserWalletMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveUserWalletVoToLiveUserWalletMapper.class,LiveUserWalletBoToLiveUserWalletMapper.class},
    imports = {}
)
public interface LiveUserWalletToLiveUserWalletVoMapper extends BaseMapper<LiveUserWallet, LiveUserWalletVo> {
}
