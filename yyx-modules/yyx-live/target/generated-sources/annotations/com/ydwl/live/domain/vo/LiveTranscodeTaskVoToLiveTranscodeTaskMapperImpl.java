package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeTask;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeTaskVoToLiveTranscodeTaskMapperImpl implements LiveTranscodeTaskVoToLiveTranscodeTaskMapper {

    @Override
    public LiveTranscodeTask convert(LiveTranscodeTaskVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeTask liveTranscodeTask = new LiveTranscodeTask();

        liveTranscodeTask.setId( arg0.getId() );
        liveTranscodeTask.setTaskNo( arg0.getTaskNo() );
        liveTranscodeTask.setTaskType( arg0.getTaskType() );
        liveTranscodeTask.setLiveId( arg0.getLiveId() );
        liveTranscodeTask.setSourceUrl( arg0.getSourceUrl() );
        liveTranscodeTask.setStatus( arg0.getStatus() );
        liveTranscodeTask.setTemplateId( arg0.getTemplateId() );
        liveTranscodeTask.setBucket( arg0.getBucket() );
        liveTranscodeTask.setObjectKey( arg0.getObjectKey() );
        liveTranscodeTask.setOutputUrl( arg0.getOutputUrl() );
        liveTranscodeTask.setDuration( arg0.getDuration() );
        liveTranscodeTask.setFileSize( arg0.getFileSize() );
        liveTranscodeTask.setProgress( arg0.getProgress() );
        liveTranscodeTask.setBizId( arg0.getBizId() );
        liveTranscodeTask.setCallbackUrl( arg0.getCallbackUrl() );
        liveTranscodeTask.setCallbackStatus( arg0.getCallbackStatus() );
        liveTranscodeTask.setStartTime( arg0.getStartTime() );
        liveTranscodeTask.setEndTime( arg0.getEndTime() );
        liveTranscodeTask.setErrorMsg( arg0.getErrorMsg() );
        liveTranscodeTask.setRetryCount( arg0.getRetryCount() );
        liveTranscodeTask.setPriority( arg0.getPriority() );

        return liveTranscodeTask;
    }

    @Override
    public LiveTranscodeTask convert(LiveTranscodeTaskVo arg0, LiveTranscodeTask arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTaskNo( arg0.getTaskNo() );
        arg1.setTaskType( arg0.getTaskType() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setSourceUrl( arg0.getSourceUrl() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTemplateId( arg0.getTemplateId() );
        arg1.setBucket( arg0.getBucket() );
        arg1.setObjectKey( arg0.getObjectKey() );
        arg1.setOutputUrl( arg0.getOutputUrl() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setBizId( arg0.getBizId() );
        arg1.setCallbackUrl( arg0.getCallbackUrl() );
        arg1.setCallbackStatus( arg0.getCallbackStatus() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setErrorMsg( arg0.getErrorMsg() );
        arg1.setRetryCount( arg0.getRetryCount() );
        arg1.setPriority( arg0.getPriority() );

        return arg1;
    }
}
