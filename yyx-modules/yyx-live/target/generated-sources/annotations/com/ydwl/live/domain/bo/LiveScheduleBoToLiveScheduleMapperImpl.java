package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveSchedule;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveScheduleBoToLiveScheduleMapperImpl implements LiveScheduleBoToLiveScheduleMapper {

    @Override
    public LiveSchedule convert(LiveScheduleBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveSchedule liveSchedule = new LiveSchedule();

        liveSchedule.setSearchValue( arg0.getSearchValue() );
        liveSchedule.setCreateDept( arg0.getCreateDept() );
        liveSchedule.setCreateBy( arg0.getCreateBy() );
        liveSchedule.setCreateTime( arg0.getCreateTime() );
        liveSchedule.setUpdateBy( arg0.getUpdateBy() );
        liveSchedule.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveSchedule.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveSchedule.setId( arg0.getId() );
        liveSchedule.setTitle( arg0.getTitle() );
        liveSchedule.setHostUserId( arg0.getHostUserId() );
        liveSchedule.setCategoryId( arg0.getCategoryId() );
        liveSchedule.setDescription( arg0.getDescription() );
        liveSchedule.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveSchedule.setScheduledTime( arg0.getScheduledTime() );
        liveSchedule.setEstimatedDurationMinutes( arg0.getEstimatedDurationMinutes() );
        liveSchedule.setScheduleStatus( arg0.getScheduleStatus() );

        return liveSchedule;
    }

    @Override
    public LiveSchedule convert(LiveScheduleBo arg0, LiveSchedule arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setHostUserId( arg0.getHostUserId() );
        arg1.setCategoryId( arg0.getCategoryId() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setScheduledTime( arg0.getScheduledTime() );
        arg1.setEstimatedDurationMinutes( arg0.getEstimatedDurationMinutes() );
        arg1.setScheduleStatus( arg0.getScheduleStatus() );

        return arg1;
    }
}
