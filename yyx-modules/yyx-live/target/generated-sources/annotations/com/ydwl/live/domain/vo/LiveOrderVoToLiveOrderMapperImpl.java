package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveOrder;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveOrderVoToLiveOrderMapperImpl implements LiveOrderVoToLiveOrderMapper {

    @Override
    public LiveOrder convert(LiveOrderVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveOrder liveOrder = new LiveOrder();

        liveOrder.setId( arg0.getId() );
        liveOrder.setOrderNo( arg0.getOrderNo() );
        liveOrder.setUserId( arg0.getUserId() );
        liveOrder.setProductId( arg0.getProductId() );
        liveOrder.setOrderType( arg0.getOrderType() );
        liveOrder.setAmount( arg0.getAmount() );
        liveOrder.setCoinAmount( arg0.getCoinAmount() );
        liveOrder.setPaymentMethod( arg0.getPaymentMethod() );
        liveOrder.setPaymentTime( arg0.getPaymentTime() );
        liveOrder.setTransactionId( arg0.getTransactionId() );
        liveOrder.setStatus( arg0.getStatus() );

        return liveOrder;
    }

    @Override
    public LiveOrder convert(LiveOrderVo arg0, LiveOrder arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setOrderNo( arg0.getOrderNo() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setProductId( arg0.getProductId() );
        arg1.setOrderType( arg0.getOrderType() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoinAmount( arg0.getCoinAmount() );
        arg1.setPaymentMethod( arg0.getPaymentMethod() );
        arg1.setPaymentTime( arg0.getPaymentTime() );
        arg1.setTransactionId( arg0.getTransactionId() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
