package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveVideoUpload;
import com.ydwl.live.domain.LiveVideoUploadToLiveVideoUploadVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveVideoUploadToLiveVideoUploadVoMapper.class},
    imports = {}
)
public interface LiveVideoUploadVoToLiveVideoUploadMapper extends BaseMapper<LiveVideoUploadVo, LiveVideoUpload> {
}
