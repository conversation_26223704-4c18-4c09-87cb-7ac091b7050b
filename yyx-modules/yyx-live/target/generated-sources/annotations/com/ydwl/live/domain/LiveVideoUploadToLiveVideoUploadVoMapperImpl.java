package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveVideoUploadVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveVideoUploadToLiveVideoUploadVoMapperImpl implements LiveVideoUploadToLiveVideoUploadVoMapper {

    @Override
    public LiveVideoUploadVo convert(LiveVideoUpload arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveVideoUploadVo liveVideoUploadVo = new LiveVideoUploadVo();

        liveVideoUploadVo.setId( arg0.getId() );
        liveVideoUploadVo.setLiveId( arg0.getLiveId() );
        liveVideoUploadVo.setFileName( arg0.getFileName() );
        liveVideoUploadVo.setFileSizeBytes( arg0.getFileSizeBytes() );
        liveVideoUploadVo.setFileType( arg0.getFileType() );
        liveVideoUploadVo.setOssUrl( arg0.getOssUrl() );
        liveVideoUploadVo.setFileMd5( arg0.getFileMd5() );
        liveVideoUploadVo.setVideoDurationSeconds( arg0.getVideoDurationSeconds() );
        liveVideoUploadVo.setVideoResolution( arg0.getVideoResolution() );
        liveVideoUploadVo.setVideoBitrateKbps( arg0.getVideoBitrateKbps() );
        liveVideoUploadVo.setFrameRate( arg0.getFrameRate() );
        liveVideoUploadVo.setVideoCodec( arg0.getVideoCodec() );
        liveVideoUploadVo.setAudioCodec( arg0.getAudioCodec() );
        liveVideoUploadVo.setAspectRatio( arg0.getAspectRatio() );
        liveVideoUploadVo.setCreatedDate( arg0.getCreatedDate() );
        liveVideoUploadVo.setLastModifiedDate( arg0.getLastModifiedDate() );
        liveVideoUploadVo.setUploadStatus( arg0.getUploadStatus() );
        liveVideoUploadVo.setUploadProgressPercent( arg0.getUploadProgressPercent() );
        liveVideoUploadVo.setUploadCompleteTime( arg0.getUploadCompleteTime() );
        liveVideoUploadVo.setErrorMessage( arg0.getErrorMessage() );

        return liveVideoUploadVo;
    }

    @Override
    public LiveVideoUploadVo convert(LiveVideoUpload arg0, LiveVideoUploadVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setFileName( arg0.getFileName() );
        arg1.setFileSizeBytes( arg0.getFileSizeBytes() );
        arg1.setFileType( arg0.getFileType() );
        arg1.setOssUrl( arg0.getOssUrl() );
        arg1.setFileMd5( arg0.getFileMd5() );
        arg1.setVideoDurationSeconds( arg0.getVideoDurationSeconds() );
        arg1.setVideoResolution( arg0.getVideoResolution() );
        arg1.setVideoBitrateKbps( arg0.getVideoBitrateKbps() );
        arg1.setFrameRate( arg0.getFrameRate() );
        arg1.setVideoCodec( arg0.getVideoCodec() );
        arg1.setAudioCodec( arg0.getAudioCodec() );
        arg1.setAspectRatio( arg0.getAspectRatio() );
        arg1.setCreatedDate( arg0.getCreatedDate() );
        arg1.setLastModifiedDate( arg0.getLastModifiedDate() );
        arg1.setUploadStatus( arg0.getUploadStatus() );
        arg1.setUploadProgressPercent( arg0.getUploadProgressPercent() );
        arg1.setUploadCompleteTime( arg0.getUploadCompleteTime() );
        arg1.setErrorMessage( arg0.getErrorMessage() );

        return arg1;
    }
}
