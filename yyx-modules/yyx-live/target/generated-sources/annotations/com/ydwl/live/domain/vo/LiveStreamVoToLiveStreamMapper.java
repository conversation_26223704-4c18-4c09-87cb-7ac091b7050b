package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveStream;
import com.ydwl.live.domain.LiveStreamToLiveStreamVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveStreamToLiveStreamVoMapper.class},
    imports = {}
)
public interface LiveStreamVoToLiveStreamMapper extends BaseMapper<LiveStreamVo, LiveStream> {
}
