package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveRoomMember;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveRoomMemberVoToLiveRoomMemberMapperImpl implements LiveRoomMemberVoToLiveRoomMemberMapper {

    @Override
    public LiveRoomMember convert(LiveRoomMemberVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveRoomMember liveRoomMember = new LiveRoomMember();

        liveRoomMember.setId( arg0.getId() );
        liveRoomMember.setLiveId( arg0.getLiveId() );
        liveRoomMember.setUserId( arg0.getUserId() );
        liveRoomMember.setJoinTime( arg0.getJoinTime() );
        liveRoomMember.setLeaveTime( arg0.getLeaveTime() );
        liveRoomMember.setWatchDurationSeconds( arg0.getWatchDurationSeconds() );
        liveRoomMember.setClientIp( arg0.getClientIp() );
        liveRoomMember.setClientDevice( arg0.getClientDevice() );

        return liveRoomMember;
    }

    @Override
    public LiveRoomMember convert(LiveRoomMemberVo arg0, LiveRoomMember arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setJoinTime( arg0.getJoinTime() );
        arg1.setLeaveTime( arg0.getLeaveTime() );
        arg1.setWatchDurationSeconds( arg0.getWatchDurationSeconds() );
        arg1.setClientIp( arg0.getClientIp() );
        arg1.setClientDevice( arg0.getClientDevice() );

        return arg1;
    }
}
