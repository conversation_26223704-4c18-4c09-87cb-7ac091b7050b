package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveAnnouncementVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveAnnouncementToLiveAnnouncementVoMapperImpl implements LiveAnnouncementToLiveAnnouncementVoMapper {

    @Override
    public LiveAnnouncementVo convert(LiveAnnouncement arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveAnnouncementVo liveAnnouncementVo = new LiveAnnouncementVo();

        liveAnnouncementVo.setId( arg0.getId() );
        liveAnnouncementVo.setLiveId( arg0.getLiveId() );
        liveAnnouncementVo.setContent( arg0.getContent() );
        liveAnnouncementVo.setStartTime( arg0.getStartTime() );
        liveAnnouncementVo.setEndTime( arg0.getEndTime() );
        liveAnnouncementVo.setAnnouncementStatus( arg0.getAnnouncementStatus() );

        return liveAnnouncementVo;
    }

    @Override
    public LiveAnnouncementVo convert(LiveAnnouncement arg0, LiveAnnouncementVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setContent( arg0.getContent() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setAnnouncementStatus( arg0.getAnnouncementStatus() );

        return arg1;
    }
}
