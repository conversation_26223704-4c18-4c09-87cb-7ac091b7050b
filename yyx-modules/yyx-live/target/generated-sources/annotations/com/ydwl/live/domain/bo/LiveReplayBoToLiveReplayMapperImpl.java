package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveReplay;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveReplayBoToLiveReplayMapperImpl implements LiveReplayBoToLiveReplayMapper {

    @Override
    public LiveReplay convert(LiveReplayBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveReplay liveReplay = new LiveReplay();

        liveReplay.setSearchValue( arg0.getSearchValue() );
        liveReplay.setCreateDept( arg0.getCreateDept() );
        liveReplay.setCreateBy( arg0.getCreateBy() );
        liveReplay.setCreateTime( arg0.getCreateTime() );
        liveReplay.setUpdateBy( arg0.getUpdateBy() );
        liveReplay.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveReplay.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveReplay.setId( arg0.getId() );
        liveReplay.setLiveId( arg0.getLiveId() );
        liveReplay.setTranscodeTaskId( arg0.getTranscodeTaskId() );
        liveReplay.setReplayUrl( arg0.getReplayUrl() );
        liveReplay.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveReplay.setStatus( arg0.getStatus() );
        liveReplay.setDuration( arg0.getDuration() );
        liveReplay.setAvailableTime( arg0.getAvailableTime() );
        liveReplay.setExpiryTime( arg0.getExpiryTime() );
        liveReplay.setAccessType( arg0.getAccessType() );
        liveReplay.setViewCount( arg0.getViewCount() );

        return liveReplay;
    }

    @Override
    public LiveReplay convert(LiveReplayBo arg0, LiveReplay arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setTranscodeTaskId( arg0.getTranscodeTaskId() );
        arg1.setReplayUrl( arg0.getReplayUrl() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setAvailableTime( arg0.getAvailableTime() );
        arg1.setExpiryTime( arg0.getExpiryTime() );
        arg1.setAccessType( arg0.getAccessType() );
        arg1.setViewCount( arg0.getViewCount() );

        return arg1;
    }
}
