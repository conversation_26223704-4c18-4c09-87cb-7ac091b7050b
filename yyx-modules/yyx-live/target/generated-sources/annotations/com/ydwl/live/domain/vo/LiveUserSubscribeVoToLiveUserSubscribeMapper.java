package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveUserSubscribe;
import com.ydwl.live.domain.LiveUserSubscribeToLiveUserSubscribeVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveUserSubscribeToLiveUserSubscribeVoMapper.class},
    imports = {}
)
public interface LiveUserSubscribeVoToLiveUserSubscribeMapper extends BaseMapper<LiveUserSubscribeVo, LiveUserSubscribe> {
}
