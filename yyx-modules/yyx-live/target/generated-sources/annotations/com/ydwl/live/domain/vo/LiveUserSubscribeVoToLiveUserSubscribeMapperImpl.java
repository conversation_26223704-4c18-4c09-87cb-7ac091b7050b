package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveUserSubscribe;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveUserSubscribeVoToLiveUserSubscribeMapperImpl implements LiveUserSubscribeVoToLiveUserSubscribeMapper {

    @Override
    public LiveUserSubscribe convert(LiveUserSubscribeVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveUserSubscribe liveUserSubscribe = new LiveUserSubscribe();

        liveUserSubscribe.setId( arg0.getId() );
        liveUserSubscribe.setUserId( arg0.getUserId() );
        liveUserSubscribe.setLiveId( arg0.getLiveId() );
        liveUserSubscribe.setSubscribeTime( arg0.getSubscribeTime() );
        liveUserSubscribe.setIsNotified( arg0.getIsNotified() );

        return liveUserSubscribe;
    }

    @Override
    public LiveUserSubscribe convert(LiveUserSubscribeVo arg0, LiveUserSubscribe arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setSubscribeTime( arg0.getSubscribeTime() );
        arg1.setIsNotified( arg0.getIsNotified() );

        return arg1;
    }
}
