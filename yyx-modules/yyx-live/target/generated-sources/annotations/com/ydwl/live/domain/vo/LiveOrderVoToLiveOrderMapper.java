package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveOrder;
import com.ydwl.live.domain.LiveOrderToLiveOrderVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveOrderToLiveOrderVoMapper.class},
    imports = {}
)
public interface LiveOrderVoToLiveOrderMapper extends BaseMapper<LiveOrderVo, LiveOrder> {
}
