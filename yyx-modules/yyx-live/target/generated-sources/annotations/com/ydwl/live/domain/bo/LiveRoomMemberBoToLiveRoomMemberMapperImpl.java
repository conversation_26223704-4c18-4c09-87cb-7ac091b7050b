package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveRoomMember;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveRoomMemberBoToLiveRoomMemberMapperImpl implements LiveRoomMemberBoToLiveRoomMemberMapper {

    @Override
    public LiveRoomMember convert(LiveRoomMemberBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveRoomMember liveRoomMember = new LiveRoomMember();

        liveRoomMember.setSearchValue( arg0.getSearchValue() );
        liveRoomMember.setCreateDept( arg0.getCreateDept() );
        liveRoomMember.setCreateBy( arg0.getCreateBy() );
        liveRoomMember.setCreateTime( arg0.getCreateTime() );
        liveRoomMember.setUpdateBy( arg0.getUpdateBy() );
        liveRoomMember.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveRoomMember.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveRoomMember.setId( arg0.getId() );
        liveRoomMember.setLiveId( arg0.getLiveId() );
        liveRoomMember.setUserId( arg0.getUserId() );
        liveRoomMember.setJoinTime( arg0.getJoinTime() );
        liveRoomMember.setLeaveTime( arg0.getLeaveTime() );
        liveRoomMember.setWatchDurationSeconds( arg0.getWatchDurationSeconds() );
        liveRoomMember.setClientIp( arg0.getClientIp() );
        liveRoomMember.setClientDevice( arg0.getClientDevice() );

        return liveRoomMember;
    }

    @Override
    public LiveRoomMember convert(LiveRoomMemberBo arg0, LiveRoomMember arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setJoinTime( arg0.getJoinTime() );
        arg1.setLeaveTime( arg0.getLeaveTime() );
        arg1.setWatchDurationSeconds( arg0.getWatchDurationSeconds() );
        arg1.setClientIp( arg0.getClientIp() );
        arg1.setClientDevice( arg0.getClientDevice() );

        return arg1;
    }
}
