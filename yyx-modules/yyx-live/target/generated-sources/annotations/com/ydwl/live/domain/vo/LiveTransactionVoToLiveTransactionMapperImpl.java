package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTransaction;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTransactionVoToLiveTransactionMapperImpl implements LiveTransactionVoToLiveTransactionMapper {

    @Override
    public LiveTransaction convert(LiveTransactionVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTransaction liveTransaction = new LiveTransaction();

        liveTransaction.setId( arg0.getId() );
        liveTransaction.setUserId( arg0.getUserId() );
        liveTransaction.setRelatedId( arg0.getRelatedId() );
        liveTransaction.setType( arg0.getType() );
        liveTransaction.setAmount( arg0.getAmount() );
        liveTransaction.setBalance( arg0.getBalance() );
        liveTransaction.setDetail( arg0.getDetail() );
        liveTransaction.setTargetType( arg0.getTargetType() );
        liveTransaction.setTargetId( arg0.getTargetId() );

        return liveTransaction;
    }

    @Override
    public LiveTransaction convert(LiveTransactionVo arg0, LiveTransaction arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setRelatedId( arg0.getRelatedId() );
        arg1.setType( arg0.getType() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setBalance( arg0.getBalance() );
        arg1.setDetail( arg0.getDetail() );
        arg1.setTargetType( arg0.getTargetType() );
        arg1.setTargetId( arg0.getTargetId() );

        return arg1;
    }
}
