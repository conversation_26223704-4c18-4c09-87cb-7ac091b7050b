package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveUserSubscribeVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveUserSubscribeToLiveUserSubscribeVoMapperImpl implements LiveUserSubscribeToLiveUserSubscribeVoMapper {

    @Override
    public LiveUserSubscribeVo convert(LiveUserSubscribe arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveUserSubscribeVo liveUserSubscribeVo = new LiveUserSubscribeVo();

        liveUserSubscribeVo.setId( arg0.getId() );
        liveUserSubscribeVo.setUserId( arg0.getUserId() );
        liveUserSubscribeVo.setLiveId( arg0.getLiveId() );
        liveUserSubscribeVo.setSubscribeTime( arg0.getSubscribeTime() );
        liveUserSubscribeVo.setIsNotified( arg0.getIsNotified() );

        return liveUserSubscribeVo;
    }

    @Override
    public LiveUserSubscribeVo convert(LiveUserSubscribe arg0, LiveUserSubscribeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setSubscribeTime( arg0.getSubscribeTime() );
        arg1.setIsNotified( arg0.getIsNotified() );

        return arg1;
    }
}
