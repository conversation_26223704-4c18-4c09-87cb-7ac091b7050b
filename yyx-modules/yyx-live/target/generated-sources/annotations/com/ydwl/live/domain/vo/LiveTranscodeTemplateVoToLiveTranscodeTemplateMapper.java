package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeTemplate;
import com.ydwl.live.domain.LiveTranscodeTemplateToLiveTranscodeTemplateVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTranscodeTemplateToLiveTranscodeTemplateVoMapper.class},
    imports = {}
)
public interface LiveTranscodeTemplateVoToLiveTranscodeTemplateMapper extends BaseMapper<LiveTranscodeTemplateVo, LiveTranscodeTemplate> {
}
