package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveEventConfig;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveEventConfigVoToLiveEventConfigMapperImpl implements LiveEventConfigVoToLiveEventConfigMapper {

    @Override
    public LiveEventConfig convert(LiveEventConfigVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveEventConfig liveEventConfig = new LiveEventConfig();

        liveEventConfig.setId( arg0.getId() );
        liveEventConfig.setEventType( arg0.getEventType() );
        liveEventConfig.setEventId( arg0.getEventId() );
        liveEventConfig.setSignupStart( arg0.getSignupStart() );
        liveEventConfig.setSignupEnd( arg0.getSignupEnd() );
        liveEventConfig.setMaxParticipants( arg0.getMaxParticipants() );
        liveEventConfig.setSignupFields( arg0.getSignupFields() );

        return liveEventConfig;
    }

    @Override
    public LiveEventConfig convert(LiveEventConfigVo arg0, LiveEventConfig arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setEventType( arg0.getEventType() );
        arg1.setEventId( arg0.getEventId() );
        arg1.setSignupStart( arg0.getSignupStart() );
        arg1.setSignupEnd( arg0.getSignupEnd() );
        arg1.setMaxParticipants( arg0.getMaxParticipants() );
        arg1.setSignupFields( arg0.getSignupFields() );

        return arg1;
    }
}
