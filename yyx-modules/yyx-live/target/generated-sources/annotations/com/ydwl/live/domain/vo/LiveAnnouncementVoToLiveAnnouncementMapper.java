package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveAnnouncement;
import com.ydwl.live.domain.LiveAnnouncementToLiveAnnouncementVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveAnnouncementToLiveAnnouncementVoMapper.class},
    imports = {}
)
public interface LiveAnnouncementVoToLiveAnnouncementMapper extends BaseMapper<LiveAnnouncementVo, LiveAnnouncement> {
}
