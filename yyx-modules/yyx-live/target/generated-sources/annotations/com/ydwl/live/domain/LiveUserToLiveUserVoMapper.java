package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveUserBoToLiveUserMapper;
import com.ydwl.live.domain.vo.LiveUserVo;
import com.ydwl.live.domain.vo.LiveUserVoToLiveUserMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveUserBoToLiveUserMapper.class,LiveUserVoToLiveUserMapper.class},
    imports = {}
)
public interface LiveUserToLiveUserVoMapper extends BaseMapper<LiveUser, LiveUserVo> {
}
