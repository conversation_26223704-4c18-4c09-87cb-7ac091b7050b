package com.ydwl.live.domain;

import com.ydwl.live.domain.bo.LiveBoToLiveMapper;
import com.ydwl.live.domain.vo.LiveVo;
import com.ydwl.live.domain.vo.LiveVoToLiveMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveVoToLiveMapper.class,LiveBoToLiveMapper.class},
    imports = {}
)
public interface LiveToLiveVoMapper extends BaseMapper<Live, LiveVo> {
}
