package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTranscodeConfig;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveTranscodeConfigVoToLiveTranscodeConfigMapperImpl implements LiveTranscodeConfigVoToLiveTranscodeConfigMapper {

    @Override
    public LiveTranscodeConfig convert(LiveTranscodeConfigVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveTranscodeConfig liveTranscodeConfig = new LiveTranscodeConfig();

        liveTranscodeConfig.setId( arg0.getId() );
        liveTranscodeConfig.setKey( arg0.getKey() );
        liveTranscodeConfig.setValue( arg0.getValue() );
        liveTranscodeConfig.setType( arg0.getType() );
        liveTranscodeConfig.setDescription( arg0.getDescription() );

        return liveTranscodeConfig;
    }

    @Override
    public LiveTranscodeConfig convert(LiveTranscodeConfigVo arg0, LiveTranscodeConfig arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setKey( arg0.getKey() );
        arg1.setValue( arg0.getValue() );
        arg1.setType( arg0.getType() );
        arg1.setDescription( arg0.getDescription() );

        return arg1;
    }
}
