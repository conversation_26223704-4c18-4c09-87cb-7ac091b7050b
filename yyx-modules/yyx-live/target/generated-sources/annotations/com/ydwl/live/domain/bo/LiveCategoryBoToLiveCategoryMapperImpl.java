package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveCategory;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveCategoryBoToLiveCategoryMapperImpl implements LiveCategoryBoToLiveCategoryMapper {

    @Override
    public LiveCategory convert(LiveCategoryBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveCategory liveCategory = new LiveCategory();

        liveCategory.setSearchValue( arg0.getSearchValue() );
        liveCategory.setCreateDept( arg0.getCreateDept() );
        liveCategory.setCreateBy( arg0.getCreateBy() );
        liveCategory.setCreateTime( arg0.getCreateTime() );
        liveCategory.setUpdateBy( arg0.getUpdateBy() );
        liveCategory.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveCategory.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveCategory.setId( arg0.getId() );
        liveCategory.setCategoryName( arg0.getCategoryName() );
        liveCategory.setCategoryCode( arg0.getCategoryCode() );
        liveCategory.setParentId( arg0.getParentId() );
        liveCategory.setPath( arg0.getPath() );
        liveCategory.setTreeSort( arg0.getTreeSort() );
        liveCategory.setTreeLevel( arg0.getTreeLevel() );
        liveCategory.setIconUrl( arg0.getIconUrl() );
        liveCategory.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveCategory.setDescription( arg0.getDescription() );
        liveCategory.setStatus( arg0.getStatus() );
        liveCategory.setLiveCount( arg0.getLiveCount() );
        liveCategory.setViewCount( arg0.getViewCount() );

        return liveCategory;
    }

    @Override
    public LiveCategory convert(LiveCategoryBo arg0, LiveCategory arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setCategoryName( arg0.getCategoryName() );
        arg1.setCategoryCode( arg0.getCategoryCode() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setPath( arg0.getPath() );
        arg1.setTreeSort( arg0.getTreeSort() );
        arg1.setTreeLevel( arg0.getTreeLevel() );
        arg1.setIconUrl( arg0.getIconUrl() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setLiveCount( arg0.getLiveCount() );
        arg1.setViewCount( arg0.getViewCount() );

        return arg1;
    }
}
