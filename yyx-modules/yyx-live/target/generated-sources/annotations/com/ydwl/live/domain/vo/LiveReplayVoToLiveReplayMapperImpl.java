package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveReplay;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveReplayVoToLiveReplayMapperImpl implements LiveReplayVoToLiveReplayMapper {

    @Override
    public LiveReplay convert(LiveReplayVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveReplay liveReplay = new LiveReplay();

        liveReplay.setId( arg0.getId() );
        liveReplay.setLiveId( arg0.getLiveId() );
        liveReplay.setTranscodeTaskId( arg0.getTranscodeTaskId() );
        liveReplay.setReplayUrl( arg0.getReplayUrl() );
        liveReplay.setCoverImgUrl( arg0.getCoverImgUrl() );
        liveReplay.setStatus( arg0.getStatus() );
        liveReplay.setDuration( arg0.getDuration() );
        liveReplay.setAvailableTime( arg0.getAvailableTime() );
        liveReplay.setExpiryTime( arg0.getExpiryTime() );
        liveReplay.setAccessType( arg0.getAccessType() );
        liveReplay.setViewCount( arg0.getViewCount() );

        return liveReplay;
    }

    @Override
    public LiveReplay convert(LiveReplayVo arg0, LiveReplay arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setTranscodeTaskId( arg0.getTranscodeTaskId() );
        arg1.setReplayUrl( arg0.getReplayUrl() );
        arg1.setCoverImgUrl( arg0.getCoverImgUrl() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setAvailableTime( arg0.getAvailableTime() );
        arg1.setExpiryTime( arg0.getExpiryTime() );
        arg1.setAccessType( arg0.getAccessType() );
        arg1.setViewCount( arg0.getViewCount() );

        return arg1;
    }
}
