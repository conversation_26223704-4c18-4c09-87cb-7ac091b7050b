package com.ydwl.live.domain.bo;

import com.ydwl.live.domain.LiveUser;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveUserBoToLiveUserMapperImpl implements LiveUserBoToLiveUserMapper {

    @Override
    public LiveUser convert(LiveUserBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveUser liveUser = new LiveUser();

        liveUser.setSearchValue( arg0.getSearchValue() );
        liveUser.setCreateDept( arg0.getCreateDept() );
        liveUser.setCreateBy( arg0.getCreateBy() );
        liveUser.setCreateTime( arg0.getCreateTime() );
        liveUser.setUpdateBy( arg0.getUpdateBy() );
        liveUser.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            liveUser.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        liveUser.setId( arg0.getId() );
        liveUser.setUsername( arg0.getUsername() );
        liveUser.setPhone( arg0.getPhone() );
        liveUser.setOpenid( arg0.getOpenid() );
        liveUser.setAvatarUrl( arg0.getAvatarUrl() );
        liveUser.setNickname( arg0.getNickname() );
        liveUser.setEmail( arg0.getEmail() );
        liveUser.setUserStatus( arg0.getUserStatus() );
        liveUser.setBio( arg0.getBio() );
        liveUser.setGender( arg0.getGender() );
        liveUser.setBirthday( arg0.getBirthday() );
        liveUser.setLastLoginTime( arg0.getLastLoginTime() );
        liveUser.setLastLoginIp( arg0.getLastLoginIp() );
        liveUser.setNotificationSettings( arg0.getNotificationSettings() );
        liveUser.setUserStatistics( arg0.getUserStatistics() );

        return liveUser;
    }

    @Override
    public LiveUser convert(LiveUserBo arg0, LiveUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUsername( arg0.getUsername() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setOpenid( arg0.getOpenid() );
        arg1.setAvatarUrl( arg0.getAvatarUrl() );
        arg1.setNickname( arg0.getNickname() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setUserStatus( arg0.getUserStatus() );
        arg1.setBio( arg0.getBio() );
        arg1.setGender( arg0.getGender() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setLastLoginTime( arg0.getLastLoginTime() );
        arg1.setLastLoginIp( arg0.getLastLoginIp() );
        arg1.setNotificationSettings( arg0.getNotificationSettings() );
        arg1.setUserStatistics( arg0.getUserStatistics() );

        return arg1;
    }
}
