package com.ydwl.live.domain;

import com.ydwl.live.domain.vo.LiveBannedUserVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveBannedUserToLiveBannedUserVoMapperImpl implements LiveBannedUserToLiveBannedUserVoMapper {

    @Override
    public LiveBannedUserVo convert(LiveBannedUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveBannedUserVo liveBannedUserVo = new LiveBannedUserVo();

        liveBannedUserVo.setId( arg0.getId() );
        liveBannedUserVo.setLiveId( arg0.getLiveId() );
        liveBannedUserVo.setUserId( arg0.getUserId() );
        liveBannedUserVo.setBanReason( arg0.getBanReason() );
        liveBannedUserVo.setOperatorId( arg0.getOperatorId() );
        liveBannedUserVo.setBanTime( arg0.getBanTime() );
        liveBannedUserVo.setUnbanTime( arg0.getUnbanTime() );
        liveBannedUserVo.setBanStatus( arg0.getBanStatus() );

        return liveBannedUserVo;
    }

    @Override
    public LiveBannedUserVo convert(LiveBannedUser arg0, LiveBannedUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBanReason( arg0.getBanReason() );
        arg1.setOperatorId( arg0.getOperatorId() );
        arg1.setBanTime( arg0.getBanTime() );
        arg1.setUnbanTime( arg0.getUnbanTime() );
        arg1.setBanStatus( arg0.getBanStatus() );

        return arg1;
    }
}
