package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveAnnouncement;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-09T18:31:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class LiveAnnouncementVoToLiveAnnouncementMapperImpl implements LiveAnnouncementVoToLiveAnnouncementMapper {

    @Override
    public LiveAnnouncement convert(LiveAnnouncementVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LiveAnnouncement liveAnnouncement = new LiveAnnouncement();

        liveAnnouncement.setId( arg0.getId() );
        liveAnnouncement.setLiveId( arg0.getLiveId() );
        liveAnnouncement.setContent( arg0.getContent() );
        liveAnnouncement.setStartTime( arg0.getStartTime() );
        liveAnnouncement.setEndTime( arg0.getEndTime() );
        liveAnnouncement.setAnnouncementStatus( arg0.getAnnouncementStatus() );

        return liveAnnouncement;
    }

    @Override
    public LiveAnnouncement convert(LiveAnnouncementVo arg0, LiveAnnouncement arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setLiveId( arg0.getLiveId() );
        arg1.setContent( arg0.getContent() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setAnnouncementStatus( arg0.getAnnouncementStatus() );

        return arg1;
    }
}
