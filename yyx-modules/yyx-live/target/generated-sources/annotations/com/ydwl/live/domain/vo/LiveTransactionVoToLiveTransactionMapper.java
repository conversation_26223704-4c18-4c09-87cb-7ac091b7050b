package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveTransaction;
import com.ydwl.live.domain.LiveTransactionToLiveTransactionVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveTransactionToLiveTransactionVoMapper.class},
    imports = {}
)
public interface LiveTransactionVoToLiveTransactionMapper extends BaseMapper<LiveTransactionVo, LiveTransaction> {
}
