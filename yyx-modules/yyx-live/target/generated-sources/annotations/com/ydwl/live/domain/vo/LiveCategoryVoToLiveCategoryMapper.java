package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveCategory;
import com.ydwl.live.domain.LiveCategoryToLiveCategoryVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveCategoryToLiveCategoryVoMapper.class},
    imports = {}
)
public interface LiveCategoryVoToLiveCategoryMapper extends BaseMapper<LiveCategoryVo, LiveCategory> {
}
