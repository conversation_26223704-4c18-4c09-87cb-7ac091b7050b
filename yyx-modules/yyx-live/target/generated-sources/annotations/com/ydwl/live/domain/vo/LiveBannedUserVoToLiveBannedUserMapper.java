package com.ydwl.live.domain.vo;

import com.ydwl.live.domain.LiveBannedUser;
import com.ydwl.live.domain.LiveBannedUserToLiveBannedUserVoMapper;
import io.github.linpeilie.AutoMapperConfig__873;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__873.class,
    uses = {LiveBannedUserToLiveBannedUserVoMapper.class},
    imports = {}
)
public interface LiveBannedUserVoToLiveBannedUserMapper extends BaseMapper<LiveBannedUserVo, LiveBannedUser> {
}
