package com.ydwl.demo.domain.vo;

import com.ydwl.demo.domain.TestDemo;
import com.ydwl.demo.domain.TestDemoToTestDemoVoMapper;
import io.github.linpeilie.AutoMapperConfig__872;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__872.class,
    uses = {TestDemoToTestDemoVoMapper.class},
    imports = {}
)
public interface TestDemoVoToTestDemoMapper extends BaseMapper<TestDemoVo, TestDemo> {
}
