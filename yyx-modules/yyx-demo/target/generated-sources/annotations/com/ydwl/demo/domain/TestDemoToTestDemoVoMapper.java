package com.ydwl.demo.domain;

import com.ydwl.demo.domain.bo.TestDemoBoToTestDemoMapper;
import com.ydwl.demo.domain.vo.TestDemoVo;
import com.ydwl.demo.domain.vo.TestDemoVoToTestDemoMapper;
import io.github.linpeilie.AutoMapperConfig__872;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__872.class,
    uses = {TestDemoBoToTestDemoMapper.class,TestDemoVoToTestDemoMapper.class},
    imports = {}
)
public interface TestDemoToTestDemoVoMapper extends BaseMapper<TestDemo, TestDemoVo> {
}
