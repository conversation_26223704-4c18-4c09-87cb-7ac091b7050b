package com.ydwl.demo.domain;

import com.ydwl.demo.domain.bo.TestTreeBoToTestTreeMapper;
import com.ydwl.demo.domain.vo.TestTreeVo;
import com.ydwl.demo.domain.vo.TestTreeVoToTestTreeMapper;
import io.github.linpeilie.AutoMapperConfig__872;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__872.class,
    uses = {TestTreeVoToTestTreeMapper.class,TestTreeBoToTestTreeMapper.class},
    imports = {}
)
public interface TestTreeToTestTreeVoMapper extends BaseMapper<TestTree, TestTreeVo> {
}
