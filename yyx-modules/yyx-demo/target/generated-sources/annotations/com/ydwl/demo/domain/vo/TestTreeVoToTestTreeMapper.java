package com.ydwl.demo.domain.vo;

import com.ydwl.demo.domain.TestTree;
import com.ydwl.demo.domain.TestTreeToTestTreeVoMapper;
import io.github.linpeilie.AutoMapperConfig__872;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__872.class,
    uses = {TestTreeToTestTreeVoMapper.class},
    imports = {}
)
public interface TestTreeVoToTestTreeMapper extends BaseMapper<TestTreeVo, TestTree> {
}
