package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysOperLogBoToSysOperLogMapper;
import com.ydwl.system.domain.vo.SysOperLogVo;
import com.ydwl.system.domain.vo.SysOperLogVoToSysOperLogMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysOperLogVoToSysOperLogMapper.class,SysOperLogBoToSysOperLogMapper.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper extends BaseMapper<SysOperLog, SysOperLogVo> {
}
