package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysDictDataBoToSysDictDataMapper;
import com.ydwl.system.domain.vo.SysDictDataVo;
import com.ydwl.system.domain.vo.SysDictDataVoToSysDictDataMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysDictDataVoToSysDictDataMapper.class,SysDictDataBoToSysDictDataMapper.class},
    imports = {}
)
public interface SysDictDataToSysDictDataVoMapper extends BaseMapper<SysDictData, SysDictDataVo> {
}
