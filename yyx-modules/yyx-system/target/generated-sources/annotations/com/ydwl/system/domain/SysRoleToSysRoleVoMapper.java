package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysRoleBoToSysRoleMapper;
import com.ydwl.system.domain.vo.SysRoleVo;
import com.ydwl.system.domain.vo.SysRoleVoToSysRoleMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysRoleVoToSysRoleMapper.class,SysRoleBoToSysRoleMapper.class},
    imports = {}
)
public interface SysRoleToSysRoleVoMapper extends BaseMapper<SysRole, SysRoleVo> {
}
