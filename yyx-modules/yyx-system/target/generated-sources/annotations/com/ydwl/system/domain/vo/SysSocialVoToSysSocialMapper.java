package com.ydwl.system.domain.vo;

import com.ydwl.system.domain.SysSocial;
import com.ydwl.system.domain.SysSocialToSysSocialVoMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysSocialToSysSocialVoMapper.class},
    imports = {}
)
public interface SysSocialVoToSysSocialMapper extends BaseMapper<SysSocialVo, SysSocial> {
}
