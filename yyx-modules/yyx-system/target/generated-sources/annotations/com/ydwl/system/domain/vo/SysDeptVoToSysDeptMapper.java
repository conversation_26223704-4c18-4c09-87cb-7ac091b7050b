package com.ydwl.system.domain.vo;

import com.ydwl.system.domain.SysDept;
import com.ydwl.system.domain.SysDeptToSysDeptVoMapper;
import com.ydwl.system.domain.bo.SysDeptBoToSysDeptMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysDeptBoToSysDeptMapper.class,SysDeptToSysDeptVoMapper.class,SysDeptToSysDeptVoMapper.class},
    imports = {}
)
public interface SysDeptVoToSysDeptMapper extends BaseMapper<SysDeptVo, SysDept> {
}
