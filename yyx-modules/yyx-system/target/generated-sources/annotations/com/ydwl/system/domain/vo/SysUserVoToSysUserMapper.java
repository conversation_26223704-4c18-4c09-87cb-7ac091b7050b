package com.ydwl.system.domain.vo;

import com.ydwl.system.domain.SysRoleToSysRoleVoMapper;
import com.ydwl.system.domain.SysUser;
import com.ydwl.system.domain.SysUserToSysUserVoMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysRoleVoToSysRoleMapper.class,SysRoleToSysRoleVoMapper.class,SysUserToSysUserVoMapper.class},
    imports = {}
)
public interface SysUserVoToSysUserMapper extends BaseMapper<SysUserVo, SysUser> {
}
