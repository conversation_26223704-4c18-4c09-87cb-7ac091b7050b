package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysClientBoToSysClientMapper;
import com.ydwl.system.domain.vo.SysClientVo;
import com.ydwl.system.domain.vo.SysClientVoToSysClientMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysClientVoToSysClientMapper.class,SysClientBoToSysClientMapper.class},
    imports = {}
)
public interface SysClientToSysClientVoMapper extends BaseMapper<SysClient, SysClientVo> {
}
