package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysOssBoToSysOssMapper;
import com.ydwl.system.domain.vo.SysOssVo;
import com.ydwl.system.domain.vo.SysOssVoToSysOssMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysOssBoToSysOssMapper.class,SysOssVoToSysOssMapper.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper extends BaseMapper<SysOss, SysOssVo> {
}
