package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysConfigBoToSysConfigMapper;
import com.ydwl.system.domain.vo.SysConfigVo;
import com.ydwl.system.domain.vo.SysConfigVoToSysConfigMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysConfigVoToSysConfigMapper.class,SysConfigBoToSysConfigMapper.class},
    imports = {}
)
public interface SysConfigToSysConfigVoMapper extends BaseMapper<SysConfig, SysConfigVo> {
}
