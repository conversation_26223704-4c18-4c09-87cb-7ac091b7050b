package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysLogininforBoToSysLogininforMapper;
import com.ydwl.system.domain.vo.SysLogininforVo;
import com.ydwl.system.domain.vo.SysLogininforVoToSysLogininforMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysLogininforVoToSysLogininforMapper.class,SysLogininforBoToSysLogininforMapper.class},
    imports = {}
)
public interface SysLogininforToSysLogininforVoMapper extends BaseMapper<SysLogininfor, SysLogininforVo> {
}
