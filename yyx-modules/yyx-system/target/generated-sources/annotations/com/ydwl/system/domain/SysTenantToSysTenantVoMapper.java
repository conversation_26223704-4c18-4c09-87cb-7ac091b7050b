package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysTenantBoToSysTenantMapper;
import com.ydwl.system.domain.vo.SysTenantVo;
import com.ydwl.system.domain.vo.SysTenantVoToSysTenantMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysTenantBoToSysTenantMapper.class,SysTenantVoToSysTenantMapper.class},
    imports = {}
)
public interface SysTenantToSysTenantVoMapper extends BaseMapper<SysTenant, SysTenantVo> {
}
