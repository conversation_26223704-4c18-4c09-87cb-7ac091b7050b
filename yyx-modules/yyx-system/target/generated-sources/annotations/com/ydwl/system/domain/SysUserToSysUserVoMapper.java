package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysUserBoToSysUserMapper;
import com.ydwl.system.domain.vo.SysRoleVoToSysRoleMapper;
import com.ydwl.system.domain.vo.SysUserVo;
import com.ydwl.system.domain.vo.SysUserVoToSysUserMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysRoleVoToSysRoleMapper.class,SysRoleToSysRoleVoMapper.class,SysUserVoToSysUserMapper.class,SysUserBoToSysUserMapper.class},
    imports = {}
)
public interface SysUserToSysUserVoMapper extends BaseMapper<SysUser, SysUserVo> {
}
