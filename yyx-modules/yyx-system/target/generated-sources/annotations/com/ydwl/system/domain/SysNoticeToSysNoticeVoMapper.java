package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysNoticeBoToSysNoticeMapper;
import com.ydwl.system.domain.vo.SysNoticeVo;
import com.ydwl.system.domain.vo.SysNoticeVoToSysNoticeMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysNoticeBoToSysNoticeMapper.class,SysNoticeVoToSysNoticeMapper.class},
    imports = {}
)
public interface SysNoticeToSysNoticeVoMapper extends BaseMapper<SysNotice, SysNoticeVo> {
}
