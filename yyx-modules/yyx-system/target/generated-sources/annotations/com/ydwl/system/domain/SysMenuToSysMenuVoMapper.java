package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysMenuBoToSysMenuMapper;
import com.ydwl.system.domain.vo.SysMenuVo;
import com.ydwl.system.domain.vo.SysMenuVoToSysMenuMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysMenuVoToSysMenuMapper.class,SysMenuBoToSysMenuMapper.class},
    imports = {}
)
public interface SysMenuToSysMenuVoMapper extends BaseMapper<SysMenu, SysMenuVo> {
}
