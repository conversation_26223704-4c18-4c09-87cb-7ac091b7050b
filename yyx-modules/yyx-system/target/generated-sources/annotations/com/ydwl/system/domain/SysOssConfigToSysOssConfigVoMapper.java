package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysOssConfigBoToSysOssConfigMapper;
import com.ydwl.system.domain.vo.SysOssConfigVo;
import com.ydwl.system.domain.vo.SysOssConfigVoToSysOssConfigMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysOssConfigBoToSysOssConfigMapper.class,SysOssConfigVoToSysOssConfigMapper.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
