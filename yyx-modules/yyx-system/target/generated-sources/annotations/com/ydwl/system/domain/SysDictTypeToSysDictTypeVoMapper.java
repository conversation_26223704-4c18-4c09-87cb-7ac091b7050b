package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysDictTypeBoToSysDictTypeMapper;
import com.ydwl.system.domain.vo.SysDictTypeVo;
import com.ydwl.system.domain.vo.SysDictTypeVoToSysDictTypeMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysDictTypeBoToSysDictTypeMapper.class,SysDictTypeVoToSysDictTypeMapper.class},
    imports = {}
)
public interface SysDictTypeToSysDictTypeVoMapper extends BaseMapper<SysDictType, SysDictTypeVo> {
}
