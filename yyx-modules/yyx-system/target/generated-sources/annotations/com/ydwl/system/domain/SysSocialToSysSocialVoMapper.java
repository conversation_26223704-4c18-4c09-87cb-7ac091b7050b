package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysSocialBoToSysSocialMapper;
import com.ydwl.system.domain.vo.SysSocialVo;
import com.ydwl.system.domain.vo.SysSocialVoToSysSocialMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysSocialBoToSysSocialMapper.class,SysSocialVoToSysSocialMapper.class},
    imports = {}
)
public interface SysSocialToSysSocialVoMapper extends BaseMapper<SysSocial, SysSocialVo> {
}
