package com.ydwl.system.domain.bo;

import com.ydwl.common.log.event.OperLogEventToSysOperLogBoMapper;
import com.ydwl.system.domain.SysOperLog;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysOperLogBoToOperLogEventMapper.class,OperLogEventToSysOperLogBoMapper.class},
    imports = {}
)
public interface SysOperLogBoToSysOperLogMapper extends BaseMapper<SysOperLogBo, SysOperLog> {
}
