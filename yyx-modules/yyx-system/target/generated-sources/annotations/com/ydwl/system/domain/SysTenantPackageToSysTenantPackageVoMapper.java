package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysTenantPackageBoToSysTenantPackageMapper;
import com.ydwl.system.domain.vo.SysTenantPackageVo;
import com.ydwl.system.domain.vo.SysTenantPackageVoToSysTenantPackageMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysTenantPackageBoToSysTenantPackageMapper.class,SysTenantPackageVoToSysTenantPackageMapper.class},
    imports = {}
)
public interface SysTenantPackageToSysTenantPackageVoMapper extends BaseMapper<SysTenantPackage, SysTenantPackageVo> {
}
