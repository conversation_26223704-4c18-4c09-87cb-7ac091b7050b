package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysPostBoToSysPostMapper;
import com.ydwl.system.domain.vo.SysPostVo;
import com.ydwl.system.domain.vo.SysPostVoToSysPostMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysPostBoToSysPostMapper.class,SysPostVoToSysPostMapper.class},
    imports = {}
)
public interface SysPostToSysPostVoMapper extends BaseMapper<SysPost, SysPostVo> {
}
