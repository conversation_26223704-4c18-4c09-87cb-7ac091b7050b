package com.ydwl.system.domain.vo;

import com.ydwl.system.domain.SysRole;
import com.ydwl.system.domain.SysRoleToSysRoleVoMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysRoleToSysRoleVoMapper.class},
    imports = {}
)
public interface SysRoleVoToSysRoleMapper extends BaseMapper<SysRoleVo, SysRole> {
}
