package com.ydwl.system.domain;

import com.ydwl.system.domain.bo.SysDeptBoToSysDeptMapper;
import com.ydwl.system.domain.vo.SysDeptVo;
import com.ydwl.system.domain.vo.SysDeptVoToSysDeptMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysDeptBoToSysDeptMapper.class,SysDeptBoToSysDeptMapper.class,SysDeptVoToSysDeptMapper.class},
    imports = {}
)
public interface SysDeptToSysDeptVoMapper extends BaseMapper<SysDept, SysDeptVo> {
}
