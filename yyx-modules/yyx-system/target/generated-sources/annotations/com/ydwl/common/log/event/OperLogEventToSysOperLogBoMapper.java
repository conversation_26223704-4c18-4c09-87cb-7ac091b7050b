package com.ydwl.common.log.event;

import com.ydwl.system.domain.bo.SysOperLogBo;
import com.ydwl.system.domain.bo.SysOperLogBoToOperLogEventMapper;
import com.ydwl.system.domain.bo.SysOperLogBoToSysOperLogMapper;
import io.github.linpeilie.AutoMapperConfig__871;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__871.class,
    uses = {SysOperLogBoToSysOperLogMapper.class,SysOperLogBoToOperLogEventMapper.class},
    imports = {}
)
public interface OperLogEventToSysOperLogBoMapper extends BaseMapper<OperLogEvent, SysOperLogBo> {
}
