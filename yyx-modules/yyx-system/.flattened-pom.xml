<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ydwl</groupId>
    <artifactId>yyx-modules</artifactId>
    <version>5.4.0</version>
  </parent>
  <groupId>com.ydwl</groupId>
  <artifactId>yyx-system</artifactId>
  <version>5.4.0</version>
  <description>system系统模块</description>
  <dependencies>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-doc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-translation</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-oss</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-log</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-sms</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-tenant</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-security</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-idempotent</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-sensitive</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-encrypt</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-websocket</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ydwl</groupId>
      <artifactId>yyx-common-sse</artifactId>
    </dependency>
  </dependencies>
</project>
